import unittest
import os
import sys
import json
from unittest.mock import MagicMock, patch

# Set up Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Mock external dependencies
sys.modules['streamlit'] = MagicMock()
sys.modules['requests'] = MagicMock()

class TestRateLimiting(unittest.TestCase):
    """Optimized test cases for API rate limiting"""
    
    def setUp(self):
        """Clean test setup with minimal mocks"""
        from app import EnhancedSolarAnalyzer
        self.analyzer = EnhancedSolarAnalyzer()
        
        # Test API keys
        self.analyzer.nasa_api_key = "test_key"
        self.analyzer.gemini_api_key = "test_key"
        self.analyzer.google_api_key = "test_key"
        
        # Reset all rate limits
        for api in self.analyzer.api_limits.values():
            api['call_count'] = 0
            api['last_called'] = 0

    def test_nasa_rate_limiting(self):
        """Simplified NASA API rate test"""
        # First 10 calls should succeed
        for _ in range(10):
            with patch('os.path.exists', return_value=True), \
                 patch('builtins.open', unittest.mock.mock_open()):
                result = self.analyzer.get_satellite_image(0, 0)
                self.assertTrue(result['success'])
        
        # 11th call should be limited
        with patch('os.path.exists', return_value=True), \
             patch('builtins.open', unittest.mock.mock_open()):
            result = self.analyzer.get_satellite_image(0, 0)
            self.assertFalse(result['success'])
            self.assertEqual(result['error'], 'NASA API rate limit exceeded')

    def test_gemini_rate_limiting(self):
        """Enhanced Gemini API rate test with complete mocking"""
        # Mock Gemini API response
        mock_response = {
            'candidates': [{
                'content': {
                    'parts': [{
                        'text': json.dumps({
                            'success': True,
                            'analysis': {
                                'optimal_panels': 10,
                                'estimated_energy': 5000
                            }
                        })
                    }]
                }
            }]
        }
        
        # First 5 calls should succeed
        for _ in range(5):
            with patch('os.path.exists', return_value=True), \
                 patch('builtins.open', unittest.mock.mock_open()), \
                 patch('requests.post') as mock_post:
                # Configure complete mock response
                mock_response_obj = MagicMock()
                mock_response_obj.status_code = 200
                mock_response_obj.json.return_value = mock_response
                
                # Validate request format
                def validate_post(url, headers, json, **kwargs):
                    self.assertEqual(headers['Content-Type'], 'application/json')
                    self.assertIn('key=', url)  # Check API key in URL
                    self.assertIn('Test', json['contents'][0]['parts'][0]['text'])  # Check address in request
                    return mock_response_obj
                
                mock_post.side_effect = validate_post
                
                result = self.analyzer.analyze_with_gemini(
                    "test.jpg", "Test", {}
                )
                self.assertTrue(result['success'])
                self.assertIn('analysis', result)
        
        # 6th call should be limited
        with patch('os.path.exists', return_value=True), \
             patch('builtins.open', unittest.mock.mock_open()):
            result = self.analyzer.analyze_with_gemini(
                "test.jpg", "Test", {}
            )
            self.assertFalse(result['success'])
            self.assertEqual(result['error'], 'Gemini API rate limit exceeded')
            self.assertIn('retry_after', result)

if __name__ == '__main__':
    unittest.main()