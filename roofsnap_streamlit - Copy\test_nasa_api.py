import json
from app import EnhancedSolarAnalyzer

def test_satellite_image():
    analyzer = EnhancedSolarAnalyzer()
    result = analyzer.get_satellite_image(34.0522, -118.2437)
    print("=== TEST RESULTS ===")
    print(json.dumps(result, indent=2))
    print("===================")
    
    if result.get('success'):
        print("✅ Test passed - Image retrieved successfully")
    else:
        print(f"❌ Test failed - {result.get('error', 'Unknown error')}")

if __name__ == "__main__":
    test_satellite_image()