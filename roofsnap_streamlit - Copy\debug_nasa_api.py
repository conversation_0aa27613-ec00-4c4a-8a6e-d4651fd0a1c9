import requests
from datetime import datetime, timedelta
import time
import os

def test_nasa_api():
    print("🚀 Starting NASA API Test")
    
    # API parameters
    url = "https://api.nasa.gov/planetary/earth/imagery"
    params = {
        'lat': 34.0522,
        'lon': -118.2437,
        'dim': 0.1,
        'date': (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d'),
        'cloud_score': False,
        'api_key': 'DEMO_KEY'
    }
    
    print(f"🌐 Making request to: {url}")
    print(f"🔧 Parameters: {params}")
    
    try:
        response = requests.get(url, params=params, timeout=10)
        print(f"🔍 Response Status: {response.status_code}")
        print(f"📋 Response Headers: {response.headers}")
        
        if response.status_code == 200:
            print("✅ Successfully received response")
            print(f"🖼️ Content Type: {response.headers.get('content-type')}")
            
            # Try saving the image
            os.makedirs('temp', exist_ok=True)
            image_path = 'temp/satellite_image.jpg'
            with open(image_path, 'wb') as f:
                f.write(response.content)
            print(f"💾 Saved image to: {image_path}")
        else:
            print(f"❌ Error response: {response.text[:200]}")
            
    except Exception as e:
        print(f"🔥 Exception occurred: {str(e)}")

if __name__ == "__main__":
    test_nasa_api()