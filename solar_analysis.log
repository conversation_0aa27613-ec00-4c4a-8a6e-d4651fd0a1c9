Solar Analysis System Log
========================================
{"timestamp": "2025-07-05 20:59:42", "type": "API_REQUEST", "message": "Requesting NASA satellite image for lat=34.0522, lng=-118.2437, date=2025-06-05", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 34.0522, "lon": -118.2437, "date": "2025-06-05", "dim": 0.1}}
{"timestamp": "2025-07-05 20:59:44", "type": "API_ERROR", "message": "NASA API error: 500", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 1}, "response": "<!DOCTYPE HTML PUBLIC \"-//IETF//DTD HTML 2.0//EN\">\n<html><head>\n<title>500 Internal Server Error</title>\n</head><body>\n<h1>Internal Server Error</h1>\n<p>The server encountered an internal error or\nmis"}
{"timestamp": "2025-07-05 20:59:44", "type": "RESOURCE", "message": "No valid satellite images available", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 2}, "available_images": ["temp/satellite_single_house.jpg", "temp/satellite_professional_highlighted.jpg", "temp/test_satellite.jpg"]}
{"timestamp": "2025-07-05 21:03:01", "type": "API_REQUEST", "message": "Requesting NASA satellite image for lat=37.7749, lng=-122.4194, date=2025-06-05", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 37.7749, "lon": -122.4194, "date": "2025-06-05", "dim": 0.1}}
{"timestamp": "2025-07-05 21:03:04", "type": "API_ERROR", "message": "NASA API error: 500", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 1}, "response": "<!DOCTYPE HTML PUBLIC \"-//IETF//DTD HTML 2.0//EN\">\n<html><head>\n<title>500 Internal Server Error</title>\n</head><body>\n<h1>Internal Server Error</h1>\n<p>The server encountered an internal error or\nmis"}
{"timestamp": "2025-07-05 21:03:04", "type": "RESOURCE", "message": "No valid satellite images available", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 2}, "available_images": ["temp/satellite_single_house.jpg", "temp/satellite_professional_highlighted.jpg", "temp/test_satellite.jpg"]}
{"timestamp": "2025-07-05 21:09:42", "type": "API_REQUEST", "message": "Requesting NASA satellite image for lat=37.7749, lng=-122.4194, date=2025-06-05", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 37.7749, "lon": -122.4194, "date": "2025-06-05", "dim": 0.1}}
{"timestamp": "2025-07-05 21:09:44", "type": "API_REQUEST", "message": "Requesting NASA satellite image for lat=37.7749, lng=-122.4194, date=2025-06-05", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 37.7749, "lon": -122.4194, "date": "2025-06-05", "dim": 0.1}}
{"timestamp": "2025-07-05 21:09:44", "type": "API_ERROR", "message": "NASA API error: 500", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 1}, "response": "<!DOCTYPE HTML PUBLIC \"-//IETF//DTD HTML 2.0//EN\">\n<html><head>\n<title>500 Internal Server Error</title>\n</head><body>\n<h1>Internal Server Error</h1>\n<p>The server encountered an internal error or\nmis"}
{"timestamp": "2025-07-05 21:09:44", "type": "RESOURCE", "message": "No valid satellite images available", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 2}, "available_images": ["temp/satellite_single_house.jpg", "temp/satellite_professional_highlighted.jpg", "temp/test_satellite.jpg"]}
{"timestamp": "2025-07-05 21:09:45", "type": "API_REQUEST", "message": "Requesting NASA satellite image for lat=37.7749, lng=-122.4194, date=2025-06-05", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 37.7749, "lon": -122.4194, "date": "2025-06-05", "dim": 0.1}}
{"timestamp": "2025-07-05 21:09:47", "type": "API_ERROR", "message": "NASA API error: 500", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 1}, "response": "<!DOCTYPE HTML PUBLIC \"-//IETF//DTD HTML 2.0//EN\">\n<html><head>\n<title>500 Internal Server Error</title>\n</head><body>\n<h1>Internal Server Error</h1>\n<p>The server encountered an internal error or\nmis"}
{"timestamp": "2025-07-05 21:09:47", "type": "RESOURCE", "message": "No valid satellite images available", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 2}, "available_images": ["temp/satellite_single_house.jpg", "temp/satellite_professional_highlighted.jpg", "temp/test_satellite.jpg"]}
{"timestamp": "2025-07-05 21:09:48", "type": "API_ERROR", "message": "NASA API error: 500", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 1}, "response": "<!DOCTYPE HTML PUBLIC \"-//IETF//DTD HTML 2.0//EN\">\n<html><head>\n<title>500 Internal Server Error</title>\n</head><body>\n<h1>Internal Server Error</h1>\n<p>The server encountered an internal error or\nmis"}
{"timestamp": "2025-07-05 21:09:48", "type": "RESOURCE", "message": "No valid satellite images available", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 2}, "available_images": ["temp/satellite_single_house.jpg", "temp/satellite_professional_highlighted.jpg", "temp/test_satellite.jpg"]}
