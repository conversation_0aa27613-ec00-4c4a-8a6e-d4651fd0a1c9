Solar Analysis System Log
========================================
{"timestamp": "2025-07-05 20:59:42", "type": "API_REQUEST", "message": "Requesting NASA satellite image for lat=34.0522, lng=-118.2437, date=2025-06-05", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 34.0522, "lon": -118.2437, "date": "2025-06-05", "dim": 0.1}}
{"timestamp": "2025-07-05 20:59:44", "type": "API_ERROR", "message": "NASA API error: 500", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 1}, "response": "<!DOCTYPE HTML PUBLIC \"-//IETF//DTD HTML 2.0//EN\">\n<html><head>\n<title>500 Internal Server Error</title>\n</head><body>\n<h1>Internal Server Error</h1>\n<p>The server encountered an internal error or\nmis"}
{"timestamp": "2025-07-05 20:59:44", "type": "RESOURCE", "message": "No valid satellite images available", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 2}, "available_images": ["temp/satellite_single_house.jpg", "temp/satellite_professional_highlighted.jpg", "temp/test_satellite.jpg"]}
{"timestamp": "2025-07-05 21:03:01", "type": "API_REQUEST", "message": "Requesting NASA satellite image for lat=37.7749, lng=-122.4194, date=2025-06-05", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 37.7749, "lon": -122.4194, "date": "2025-06-05", "dim": 0.1}}
{"timestamp": "2025-07-05 21:03:04", "type": "API_ERROR", "message": "NASA API error: 500", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 1}, "response": "<!DOCTYPE HTML PUBLIC \"-//IETF//DTD HTML 2.0//EN\">\n<html><head>\n<title>500 Internal Server Error</title>\n</head><body>\n<h1>Internal Server Error</h1>\n<p>The server encountered an internal error or\nmis"}
{"timestamp": "2025-07-05 21:03:04", "type": "RESOURCE", "message": "No valid satellite images available", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 2}, "available_images": ["temp/satellite_single_house.jpg", "temp/satellite_professional_highlighted.jpg", "temp/test_satellite.jpg"]}
{"timestamp": "2025-07-05 21:09:42", "type": "API_REQUEST", "message": "Requesting NASA satellite image for lat=37.7749, lng=-122.4194, date=2025-06-05", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 37.7749, "lon": -122.4194, "date": "2025-06-05", "dim": 0.1}}
{"timestamp": "2025-07-05 21:09:44", "type": "API_REQUEST", "message": "Requesting NASA satellite image for lat=37.7749, lng=-122.4194, date=2025-06-05", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 37.7749, "lon": -122.4194, "date": "2025-06-05", "dim": 0.1}}
{"timestamp": "2025-07-05 21:09:44", "type": "API_ERROR", "message": "NASA API error: 500", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 1}, "response": "<!DOCTYPE HTML PUBLIC \"-//IETF//DTD HTML 2.0//EN\">\n<html><head>\n<title>500 Internal Server Error</title>\n</head><body>\n<h1>Internal Server Error</h1>\n<p>The server encountered an internal error or\nmis"}
{"timestamp": "2025-07-05 21:09:44", "type": "RESOURCE", "message": "No valid satellite images available", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 2}, "available_images": ["temp/satellite_single_house.jpg", "temp/satellite_professional_highlighted.jpg", "temp/test_satellite.jpg"]}
{"timestamp": "2025-07-05 21:09:45", "type": "API_REQUEST", "message": "Requesting NASA satellite image for lat=37.7749, lng=-122.4194, date=2025-06-05", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 37.7749, "lon": -122.4194, "date": "2025-06-05", "dim": 0.1}}
{"timestamp": "2025-07-05 21:09:47", "type": "API_ERROR", "message": "NASA API error: 500", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 1}, "response": "<!DOCTYPE HTML PUBLIC \"-//IETF//DTD HTML 2.0//EN\">\n<html><head>\n<title>500 Internal Server Error</title>\n</head><body>\n<h1>Internal Server Error</h1>\n<p>The server encountered an internal error or\nmis"}
{"timestamp": "2025-07-05 21:09:47", "type": "RESOURCE", "message": "No valid satellite images available", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 2}, "available_images": ["temp/satellite_single_house.jpg", "temp/satellite_professional_highlighted.jpg", "temp/test_satellite.jpg"]}
{"timestamp": "2025-07-05 21:09:48", "type": "API_ERROR", "message": "NASA API error: 500", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 1}, "response": "<!DOCTYPE HTML PUBLIC \"-//IETF//DTD HTML 2.0//EN\">\n<html><head>\n<title>500 Internal Server Error</title>\n</head><body>\n<h1>Internal Server Error</h1>\n<p>The server encountered an internal error or\nmis"}
{"timestamp": "2025-07-05 21:09:48", "type": "RESOURCE", "message": "No valid satellite images available", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 2}, "available_images": ["temp/satellite_single_house.jpg", "temp/satellite_professional_highlighted.jpg", "temp/test_satellite.jpg"]}
{"timestamp": "2025-07-05 21:11:45", "type": "API_REQUEST", "message": "Requesting NASA satellite image for lat=37.7749, lng=-122.4194, date=2025-06-05", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 37.7749, "lon": -122.4194, "date": "2025-06-05", "dim": 0.1}}
{"timestamp": "2025-07-05 21:11:48", "type": "API_ERROR", "message": "NASA API error: 500", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 1}, "response": "<!DOCTYPE HTML PUBLIC \"-//IETF//DTD HTML 2.0//EN\">\n<html><head>\n<title>500 Internal Server Error</title>\n</head><body>\n<h1>Internal Server Error</h1>\n<p>The server encountered an internal error or\nmis"}
{"timestamp": "2025-07-05 21:11:48", "type": "RESOURCE", "message": "No valid satellite images available", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 2}, "available_images": ["temp/satellite_single_house.jpg", "temp/satellite_professional_highlighted.jpg", "temp/test_satellite.jpg"]}
{"timestamp": "2025-07-05 21:17:32", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=37.4224857, lng=-122.0855846, date=2025-06-05", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 37.4224857, "lon": -122.0855846, "date": "2025-06-05", "dim": 0.15}}
{"timestamp": "2025-07-05 21:17:34", "type": "API_ERROR", "message": "NASA API error for date 2025-06-05: 500", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 1}, "response": "<!DOCTYPE HTML PUBLIC \"-//IETF//DTD HTML 2.0//EN\">\n<html><head>\n<title>500 Internal Server Error</title>\n</head><body>\n<h1>Internal Server Error</h1>\n<p>The server encountered an internal error or\nmis"}
{"timestamp": "2025-07-05 21:17:35", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=37.4224857, lng=-122.0855846, date=2025-05-06", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 2}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 37.4224857, "lon": -122.0855846, "date": "2025-05-06", "dim": 0.15}}
{"timestamp": "2025-07-05 21:17:37", "type": "API_ERROR", "message": "NASA API error for date 2025-05-06: 500", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 3}, "response": "<!DOCTYPE HTML PUBLIC \"-//IETF//DTD HTML 2.0//EN\">\n<html><head>\n<title>500 Internal Server Error</title>\n</head><body>\n<h1>Internal Server Error</h1>\n<p>The server encountered an internal error or\nmis"}
{"timestamp": "2025-07-05 21:17:37", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=37.4224857, lng=-122.0855846, date=2025-04-06", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 4}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 37.4224857, "lon": -122.0855846, "date": "2025-04-06", "dim": 0.15}}
{"timestamp": "2025-07-05 21:17:40", "type": "API_ERROR", "message": "NASA API error for date 2025-04-06: 500", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 5}, "response": "<!DOCTYPE HTML PUBLIC \"-//IETF//DTD HTML 2.0//EN\">\n<html><head>\n<title>500 Internal Server Error</title>\n</head><body>\n<h1>Internal Server Error</h1>\n<p>The server encountered an internal error or\nmis"}
{"timestamp": "2025-07-05 21:17:40", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=37.4224857, lng=-122.0855846, date=2023-06-01", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 6}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 37.4224857, "lon": -122.0855846, "date": "2023-06-01", "dim": 0.15}}
{"timestamp": "2025-07-05 21:17:43", "type": "API_ERROR", "message": "NASA API error for date 2023-06-01: 500", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 7}, "response": "<!DOCTYPE HTML PUBLIC \"-//IETF//DTD HTML 2.0//EN\">\n<html><head>\n<title>500 Internal Server Error</title>\n</head><body>\n<h1>Internal Server Error</h1>\n<p>The server encountered an internal error or\nmis"}
{"timestamp": "2025-07-05 21:17:43", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=37.4224857, lng=-122.0855846, date=2023-01-01", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 8}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 37.4224857, "lon": -122.0855846, "date": "2023-01-01", "dim": 0.15}}
{"timestamp": "2025-07-05 21:17:45", "type": "API_ERROR", "message": "NASA API error for date 2023-01-01: 500", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 9}, "response": "<!DOCTYPE HTML PUBLIC \"-//IETF//DTD HTML 2.0//EN\">\n<html><head>\n<title>500 Internal Server Error</title>\n</head><body>\n<h1>Internal Server Error</h1>\n<p>The server encountered an internal error or\nmis"}
{"timestamp": "2025-07-05 21:17:46", "type": "RESOURCE", "message": "No valid satellite images available", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 10}, "available_images": ["temp/satellite_single_house.jpg", "temp/satellite_professional_highlighted.jpg", "temp/test_satellite.jpg"]}
{"timestamp": "2025-07-05 21:20:27", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=37.7749, lng=-122.4194, date=2025-06-05", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 37.7749, "lon": -122.4194, "date": "2025-06-05", "dim": 0.15}}
{"timestamp": "2025-07-05 21:20:29", "type": "API_ERROR", "message": "NASA API error for date 2025-06-05: 500", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 1}, "response": "<!DOCTYPE HTML PUBLIC \"-//IETF//DTD HTML 2.0//EN\">\n<html><head>\n<title>500 Internal Server Error</title>\n</head><body>\n<h1>Internal Server Error</h1>\n<p>The server encountered an internal error or\nmis"}
{"timestamp": "2025-07-05 21:20:30", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=37.7749, lng=-122.4194, date=2025-05-06", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 2}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 37.7749, "lon": -122.4194, "date": "2025-05-06", "dim": 0.15}}
{"timestamp": "2025-07-05 21:20:32", "type": "API_ERROR", "message": "NASA API error for date 2025-05-06: 500", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 3}, "response": "<!DOCTYPE HTML PUBLIC \"-//IETF//DTD HTML 2.0//EN\">\n<html><head>\n<title>500 Internal Server Error</title>\n</head><body>\n<h1>Internal Server Error</h1>\n<p>The server encountered an internal error or\nmis"}
{"timestamp": "2025-07-05 21:20:33", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=37.7749, lng=-122.4194, date=2025-04-06", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 4}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 37.7749, "lon": -122.4194, "date": "2025-04-06", "dim": 0.15}}
{"timestamp": "2025-07-05 21:20:35", "type": "API_ERROR", "message": "NASA API error for date 2025-04-06: 500", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 5}, "response": "<!DOCTYPE HTML PUBLIC \"-//IETF//DTD HTML 2.0//EN\">\n<html><head>\n<title>500 Internal Server Error</title>\n</head><body>\n<h1>Internal Server Error</h1>\n<p>The server encountered an internal error or\nmis"}
{"timestamp": "2025-07-05 21:20:35", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=37.7749, lng=-122.4194, date=2025-06-05", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 37.7749, "lon": -122.4194, "date": "2025-06-05", "dim": 0.15}}
{"timestamp": "2025-07-05 21:20:35", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=37.7749, lng=-122.4194, date=2023-06-01", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 6}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 37.7749, "lon": -122.4194, "date": "2023-06-01", "dim": 0.15}}
{"timestamp": "2025-07-05 21:20:37", "type": "API_ERROR", "message": "NASA API error for date 2025-06-05: 500", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 1}, "response": "<!DOCTYPE HTML PUBLIC \"-//IETF//DTD HTML 2.0//EN\">\n<html><head>\n<title>500 Internal Server Error</title>\n</head><body>\n<h1>Internal Server Error</h1>\n<p>The server encountered an internal error or\nmis"}
{"timestamp": "2025-07-05 21:20:37", "type": "API_ERROR", "message": "NASA API error for date 2023-06-01: 500", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 7}, "response": "<!DOCTYPE HTML PUBLIC \"-//IETF//DTD HTML 2.0//EN\">\n<html><head>\n<title>500 Internal Server Error</title>\n</head><body>\n<h1>Internal Server Error</h1>\n<p>The server encountered an internal error or\nmis"}
{"timestamp": "2025-07-05 21:20:38", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=37.7749, lng=-122.4194, date=2025-05-06", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 2}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 37.7749, "lon": -122.4194, "date": "2025-05-06", "dim": 0.15}}
{"timestamp": "2025-07-05 21:20:38", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=37.7749, lng=-122.4194, date=2023-01-01", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 8}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 37.7749, "lon": -122.4194, "date": "2023-01-01", "dim": 0.15}}
{"timestamp": "2025-07-05 21:20:40", "type": "API_ERROR", "message": "NASA API error for date 2025-05-06: 500", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 3}, "response": "<!DOCTYPE HTML PUBLIC \"-//IETF//DTD HTML 2.0//EN\">\n<html><head>\n<title>500 Internal Server Error</title>\n</head><body>\n<h1>Internal Server Error</h1>\n<p>The server encountered an internal error or\nmis"}
{"timestamp": "2025-07-05 21:20:41", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=37.7749, lng=-122.4194, date=2025-04-06", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 4}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 37.7749, "lon": -122.4194, "date": "2025-04-06", "dim": 0.15}}
{"timestamp": "2025-07-05 21:20:41", "type": "API_ERROR", "message": "NASA API error for date 2023-01-01: 500", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 9}, "response": "<!DOCTYPE HTML PUBLIC \"-//IETF//DTD HTML 2.0//EN\">\n<html><head>\n<title>500 Internal Server Error</title>\n</head><body>\n<h1>Internal Server Error</h1>\n<p>The server encountered an internal error or\nmis"}
{"timestamp": "2025-07-05 21:20:42", "type": "RESOURCE", "message": "No valid satellite images available", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 10}, "available_images": ["temp/satellite_single_house.jpg", "temp/satellite_professional_highlighted.jpg", "temp/test_satellite.jpg"]}
{"timestamp": "2025-07-05 21:20:43", "type": "API_ERROR", "message": "NASA API error for date 2025-04-06: 500", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 5}, "response": "<!DOCTYPE HTML PUBLIC \"-//IETF//DTD HTML 2.0//EN\">\n<html><head>\n<title>500 Internal Server Error</title>\n</head><body>\n<h1>Internal Server Error</h1>\n<p>The server encountered an internal error or\nmis"}
{"timestamp": "2025-07-05 21:20:43", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=37.7749, lng=-122.4194, date=2023-06-01", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 6}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 37.7749, "lon": -122.4194, "date": "2023-06-01", "dim": 0.15}}
{"timestamp": "2025-07-05 21:20:45", "type": "API_ERROR", "message": "NASA API error for date 2023-06-01: 500", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 7}, "response": "<!DOCTYPE HTML PUBLIC \"-//IETF//DTD HTML 2.0//EN\">\n<html><head>\n<title>500 Internal Server Error</title>\n</head><body>\n<h1>Internal Server Error</h1>\n<p>The server encountered an internal error or\nmis"}
{"timestamp": "2025-07-05 21:20:46", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=37.7749, lng=-122.4194, date=2023-01-01", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 8}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 37.7749, "lon": -122.4194, "date": "2023-01-01", "dim": 0.15}}
{"timestamp": "2025-07-05 21:20:48", "type": "API_ERROR", "message": "NASA API error for date 2023-01-01: 500", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 9}, "response": "<!DOCTYPE HTML PUBLIC \"-//IETF//DTD HTML 2.0//EN\">\n<html><head>\n<title>500 Internal Server Error</title>\n</head><body>\n<h1>Internal Server Error</h1>\n<p>The server encountered an internal error or\nmis"}
{"timestamp": "2025-07-05 21:20:48", "type": "RESOURCE", "message": "No valid satellite images available", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 10}, "available_images": ["temp/satellite_single_house.jpg", "temp/satellite_professional_highlighted.jpg", "temp/test_satellite.jpg"]}
{"timestamp": "2025-07-05 21:20:55", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=37.7749, lng=-122.4194, date=2025-06-05", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 37.7749, "lon": -122.4194, "date": "2025-06-05", "dim": 0.15}}
{"timestamp": "2025-07-05 21:20:57", "type": "API_ERROR", "message": "NASA API error for date 2025-06-05: 500", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 1}, "response": "<!DOCTYPE HTML PUBLIC \"-//IETF//DTD HTML 2.0//EN\">\n<html><head>\n<title>500 Internal Server Error</title>\n</head><body>\n<h1>Internal Server Error</h1>\n<p>The server encountered an internal error or\nmis"}
{"timestamp": "2025-07-05 21:20:58", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=37.7749, lng=-122.4194, date=2025-05-06", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 2}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 37.7749, "lon": -122.4194, "date": "2025-05-06", "dim": 0.15}}
{"timestamp": "2025-07-05 21:21:00", "type": "API_ERROR", "message": "NASA API error for date 2025-05-06: 500", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 3}, "response": "<!DOCTYPE HTML PUBLIC \"-//IETF//DTD HTML 2.0//EN\">\n<html><head>\n<title>500 Internal Server Error</title>\n</head><body>\n<h1>Internal Server Error</h1>\n<p>The server encountered an internal error or\nmis"}
{"timestamp": "2025-07-05 21:21:00", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=37.7749, lng=-122.4194, date=2025-04-06", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 4}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 37.7749, "lon": -122.4194, "date": "2025-04-06", "dim": 0.15}}
{"timestamp": "2025-07-05 21:21:01", "type": "API_ERROR", "message": "NASA API error for date 2025-04-06: 429", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 5}, "response": "{\n  \"error\": {\n    \"code\": \"OVER_RATE_LIMIT\",\n    \"message\": \"You have exceeded your rate limit. Try again later or contact us at https://api.nasa.gov:443/contact/ for assistance\"\n  }\n}"}
{"timestamp": "2025-07-05 21:21:02", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=37.7749, lng=-122.4194, date=2023-06-01", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 6}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 37.7749, "lon": -122.4194, "date": "2023-06-01", "dim": 0.15}}
{"timestamp": "2025-07-05 21:21:03", "type": "API_ERROR", "message": "NASA API error for date 2023-06-01: 429", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 7}, "response": "{\n  \"error\": {\n    \"code\": \"OVER_RATE_LIMIT\",\n    \"message\": \"You have exceeded your rate limit. Try again later or contact us at https://api.nasa.gov:443/contact/ for assistance\"\n  }\n}"}
{"timestamp": "2025-07-05 21:21:03", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=37.7749, lng=-122.4194, date=2023-01-01", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 8}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 37.7749, "lon": -122.4194, "date": "2023-01-01", "dim": 0.15}}
{"timestamp": "2025-07-05 21:21:05", "type": "API_ERROR", "message": "NASA API error for date 2023-01-01: 429", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 9}, "response": "{\n  \"error\": {\n    \"code\": \"OVER_RATE_LIMIT\",\n    \"message\": \"You have exceeded your rate limit. Try again later or contact us at https://api.nasa.gov:443/contact/ for assistance\"\n  }\n}"}
{"timestamp": "2025-07-05 21:21:05", "type": "RESOURCE", "message": "No valid satellite images available", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 10}, "available_images": ["temp/satellite_single_house.jpg", "temp/satellite_professional_highlighted.jpg", "temp/test_satellite.jpg"]}
{"timestamp": "2025-07-05 21:30:34", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=37.7749, lng=-122.4194, date=2025-06-05", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 37.7749, "lon": -122.4194, "date": "2025-06-05", "dim": 0.15}}
{"timestamp": "2025-07-05 21:30:36", "type": "API_ERROR", "message": "NASA API error for date 2025-06-05: 500", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 1}, "response": "<!DOCTYPE HTML PUBLIC \"-//IETF//DTD HTML 2.0//EN\">\n<html><head>\n<title>500 Internal Server Error</title>\n</head><body>\n<h1>Internal Server Error</h1>\n<p>The server encountered an internal error or\nmis"}
{"timestamp": "2025-07-05 21:30:37", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=37.7749, lng=-122.4194, date=2025-05-06", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 2}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 37.7749, "lon": -122.4194, "date": "2025-05-06", "dim": 0.15}}
{"timestamp": "2025-07-05 21:30:38", "type": "API_ERROR", "message": "NASA API error for date 2025-05-06: 429", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 3}, "response": "{\n  \"error\": {\n    \"code\": \"OVER_RATE_LIMIT\",\n    \"message\": \"You have exceeded your rate limit. Try again later or contact us at https://api.nasa.gov:443/contact/ for assistance\"\n  }\n}"}
{"timestamp": "2025-07-05 21:30:38", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=37.7749, lng=-122.4194, date=2025-04-06", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 4}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 37.7749, "lon": -122.4194, "date": "2025-04-06", "dim": 0.15}}
{"timestamp": "2025-07-05 21:30:39", "type": "API_ERROR", "message": "NASA API error for date 2025-04-06: 429", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 5}, "response": "{\n  \"error\": {\n    \"code\": \"OVER_RATE_LIMIT\",\n    \"message\": \"You have exceeded your rate limit. Try again later or contact us at https://api.nasa.gov:443/contact/ for assistance\"\n  }\n}"}
{"timestamp": "2025-07-05 21:30:40", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=37.7749, lng=-122.4194, date=2023-06-01", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 6}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 37.7749, "lon": -122.4194, "date": "2023-06-01", "dim": 0.15}}
{"timestamp": "2025-07-05 21:30:41", "type": "API_ERROR", "message": "NASA API error for date 2023-06-01: 429", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 7}, "response": "{\n  \"error\": {\n    \"code\": \"OVER_RATE_LIMIT\",\n    \"message\": \"You have exceeded your rate limit. Try again later or contact us at https://api.nasa.gov:443/contact/ for assistance\"\n  }\n}"}
{"timestamp": "2025-07-05 21:30:41", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=37.7749, lng=-122.4194, date=2023-01-01", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 8}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 37.7749, "lon": -122.4194, "date": "2023-01-01", "dim": 0.15}}
{"timestamp": "2025-07-05 21:30:43", "type": "API_ERROR", "message": "NASA API error for date 2023-01-01: 429", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 9}, "response": "{\n  \"error\": {\n    \"code\": \"OVER_RATE_LIMIT\",\n    \"message\": \"You have exceeded your rate limit. Try again later or contact us at https://api.nasa.gov:443/contact/ for assistance\"\n  }\n}"}
{"timestamp": "2025-07-05 21:30:43", "type": "RESOURCE", "message": "No valid satellite images available", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 10}, "available_images": ["temp/satellite_single_house.jpg", "temp/satellite_professional_highlighted.jpg", "temp/test_satellite.jpg"]}
{"timestamp": "2025-07-05 21:40:36", "type": "NO_RESULTS", "message": "No results from Nominatim for: Street:\u00a0\u00a01206 Lindale Avenue  City:\u00a0\u00a0Fremont  State/province/area: \u00a0\u00a0California  Phone number:\u00a0\u00a0************  Zip code:\u00a0\u00a094539  Country calling code:\u00a0\u00a0+1  Country:\u00a0\u00a0United States", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}}
{"timestamp": "2025-07-05 21:40:47", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=37.7749, lng=-122.4194, date=2025-06-05", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 37.7749, "lon": -122.4194, "date": "2025-06-05", "dim": 0.15}}
{"timestamp": "2025-07-05 21:40:49", "type": "API_ERROR", "message": "NASA API error for date 2025-06-05: 500", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 1}, "response": "<!DOCTYPE HTML PUBLIC \"-//IETF//DTD HTML 2.0//EN\">\n<html><head>\n<title>500 Internal Server Error</title>\n</head><body>\n<h1>Internal Server Error</h1>\n<p>The server encountered an internal error or\nmis"}
{"timestamp": "2025-07-05 21:40:50", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=37.7749, lng=-122.4194, date=2025-05-06", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 2}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 37.7749, "lon": -122.4194, "date": "2025-05-06", "dim": 0.15}}
{"timestamp": "2025-07-05 21:40:52", "type": "API_ERROR", "message": "NASA API error for date 2025-05-06: 500", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 3}, "response": "<!DOCTYPE HTML PUBLIC \"-//IETF//DTD HTML 2.0//EN\">\n<html><head>\n<title>500 Internal Server Error</title>\n</head><body>\n<h1>Internal Server Error</h1>\n<p>The server encountered an internal error or\nmis"}
{"timestamp": "2025-07-05 21:40:52", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=37.7749, lng=-122.4194, date=2025-04-06", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 4}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 37.7749, "lon": -122.4194, "date": "2025-04-06", "dim": 0.15}}
{"timestamp": "2025-07-05 21:40:55", "type": "API_ERROR", "message": "NASA API error for date 2025-04-06: 500", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 5}, "response": "<!DOCTYPE HTML PUBLIC \"-//IETF//DTD HTML 2.0//EN\">\n<html><head>\n<title>500 Internal Server Error</title>\n</head><body>\n<h1>Internal Server Error</h1>\n<p>The server encountered an internal error or\nmis"}
{"timestamp": "2025-07-05 21:40:55", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=37.7749, lng=-122.4194, date=2023-06-01", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 6}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 37.7749, "lon": -122.4194, "date": "2023-06-01", "dim": 0.15}}
{"timestamp": "2025-07-05 21:40:57", "type": "API_ERROR", "message": "NASA API error for date 2023-06-01: 500", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 7}, "response": "<!DOCTYPE HTML PUBLIC \"-//IETF//DTD HTML 2.0//EN\">\n<html><head>\n<title>500 Internal Server Error</title>\n</head><body>\n<h1>Internal Server Error</h1>\n<p>The server encountered an internal error or\nmis"}
{"timestamp": "2025-07-05 21:40:58", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=37.7749, lng=-122.4194, date=2023-01-01", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 8}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 37.7749, "lon": -122.4194, "date": "2023-01-01", "dim": 0.15}}
{"timestamp": "2025-07-05 21:41:00", "type": "API_ERROR", "message": "NASA API error for date 2023-01-01: 500", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 9}, "response": "<!DOCTYPE HTML PUBLIC \"-//IETF//DTD HTML 2.0//EN\">\n<html><head>\n<title>500 Internal Server Error</title>\n</head><body>\n<h1>Internal Server Error</h1>\n<p>The server encountered an internal error or\nmis"}
{"timestamp": "2025-07-05 21:41:00", "type": "RESOURCE", "message": "No valid satellite images available", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 10}, "available_images": ["temp/satellite_single_house.jpg", "temp/satellite_professional_highlighted.jpg", "temp/test_satellite.jpg"]}
{"timestamp": "2025-07-05 21:42:11", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=-13.164341, lng=-72.5450094, date=2025-06-05", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": -13.164341, "lon": -72.5450094, "date": "2025-06-05", "dim": 0.15}}
{"timestamp": "2025-07-05 21:42:13", "type": "API_ERROR", "message": "NASA API error for date 2025-06-05: 500", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 1}, "response": "<!DOCTYPE HTML PUBLIC \"-//IETF//DTD HTML 2.0//EN\">\n<html><head>\n<title>500 Internal Server Error</title>\n</head><body>\n<h1>Internal Server Error</h1>\n<p>The server encountered an internal error or\nmis"}
{"timestamp": "2025-07-05 21:42:14", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=-13.164341, lng=-72.5450094, date=2025-05-06", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 2}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": -13.164341, "lon": -72.5450094, "date": "2025-05-06", "dim": 0.15}}
{"timestamp": "2025-07-05 21:42:15", "type": "API_ERROR", "message": "NASA API error for date 2025-05-06: 429", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 3}, "response": "{\n  \"error\": {\n    \"code\": \"OVER_RATE_LIMIT\",\n    \"message\": \"You have exceeded your rate limit. Try again later or contact us at https://api.nasa.gov:443/contact/ for assistance\"\n  }\n}"}
{"timestamp": "2025-07-05 21:42:15", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=-13.164341, lng=-72.5450094, date=2025-04-06", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 4}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": -13.164341, "lon": -72.5450094, "date": "2025-04-06", "dim": 0.15}}
{"timestamp": "2025-07-05 21:42:16", "type": "API_ERROR", "message": "NASA API error for date 2025-04-06: 429", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 5}, "response": "{\n  \"error\": {\n    \"code\": \"OVER_RATE_LIMIT\",\n    \"message\": \"You have exceeded your rate limit. Try again later or contact us at https://api.nasa.gov:443/contact/ for assistance\"\n  }\n}"}
{"timestamp": "2025-07-05 21:42:17", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=-13.164341, lng=-72.5450094, date=2023-06-01", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 6}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": -13.164341, "lon": -72.5450094, "date": "2023-06-01", "dim": 0.15}}
{"timestamp": "2025-07-05 21:42:18", "type": "API_ERROR", "message": "NASA API error for date 2023-06-01: 429", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 7}, "response": "{\n  \"error\": {\n    \"code\": \"OVER_RATE_LIMIT\",\n    \"message\": \"You have exceeded your rate limit. Try again later or contact us at https://api.nasa.gov:443/contact/ for assistance\"\n  }\n}"}
{"timestamp": "2025-07-05 21:42:18", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=-13.164341, lng=-72.5450094, date=2023-01-01", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 8}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": -13.164341, "lon": -72.5450094, "date": "2023-01-01", "dim": 0.15}}
{"timestamp": "2025-07-05 21:42:20", "type": "API_ERROR", "message": "NASA API error for date 2023-01-01: 429", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 9}, "response": "{\n  \"error\": {\n    \"code\": \"OVER_RATE_LIMIT\",\n    \"message\": \"You have exceeded your rate limit. Try again later or contact us at https://api.nasa.gov:443/contact/ for assistance\"\n  }\n}"}
{"timestamp": "2025-07-05 21:42:20", "type": "RESOURCE", "message": "No valid satellite images available", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 10}, "available_images": ["temp/satellite_single_house.jpg", "temp/satellite_professional_highlighted.jpg", "temp/test_satellite.jpg"]}
{"timestamp": "2025-07-05 21:42:20", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=-13.164341, lng=-72.5450094, date=2025-06-05", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 11}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": -13.164341, "lon": -72.5450094, "date": "2025-06-05", "dim": 0.15}}
{"timestamp": "2025-07-05 21:42:21", "type": "API_ERROR", "message": "NASA API error for date 2025-06-05: 429", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 12}, "response": "{\n  \"error\": {\n    \"code\": \"OVER_RATE_LIMIT\",\n    \"message\": \"You have exceeded your rate limit. Try again later or contact us at https://api.nasa.gov:443/contact/ for assistance\"\n  }\n}"}
{"timestamp": "2025-07-05 21:42:22", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=-13.164341, lng=-72.5450094, date=2025-05-06", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 13}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": -13.164341, "lon": -72.5450094, "date": "2025-05-06", "dim": 0.15}}
{"timestamp": "2025-07-05 21:42:23", "type": "API_ERROR", "message": "NASA API error for date 2025-05-06: 429", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 14}, "response": "{\n  \"error\": {\n    \"code\": \"OVER_RATE_LIMIT\",\n    \"message\": \"You have exceeded your rate limit. Try again later or contact us at https://api.nasa.gov:443/contact/ for assistance\"\n  }\n}"}
{"timestamp": "2025-07-05 21:42:23", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=-13.164341, lng=-72.5450094, date=2025-04-06", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 15}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": -13.164341, "lon": -72.5450094, "date": "2025-04-06", "dim": 0.15}}
{"timestamp": "2025-07-05 21:42:24", "type": "API_ERROR", "message": "NASA API error for date 2025-04-06: 429", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 16}, "response": "{\n  \"error\": {\n    \"code\": \"OVER_RATE_LIMIT\",\n    \"message\": \"You have exceeded your rate limit. Try again later or contact us at https://api.nasa.gov:443/contact/ for assistance\"\n  }\n}"}
{"timestamp": "2025-07-05 21:42:25", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=-13.164341, lng=-72.5450094, date=2023-06-01", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 17}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": -13.164341, "lon": -72.5450094, "date": "2023-06-01", "dim": 0.15}}
{"timestamp": "2025-07-05 21:42:26", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=-13.164341, lng=-72.5450094, date=2025-06-05", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": -13.164341, "lon": -72.5450094, "date": "2025-06-05", "dim": 0.15}}
{"timestamp": "2025-07-05 21:42:26", "type": "API_ERROR", "message": "NASA API error for date 2023-06-01: 429", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 18}, "response": "{\n  \"error\": {\n    \"code\": \"OVER_RATE_LIMIT\",\n    \"message\": \"You have exceeded your rate limit. Try again later or contact us at https://api.nasa.gov:443/contact/ for assistance\"\n  }\n}"}
{"timestamp": "2025-07-05 21:42:27", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=-13.164341, lng=-72.5450094, date=2023-01-01", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 19}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": -13.164341, "lon": -72.5450094, "date": "2023-01-01", "dim": 0.15}}
{"timestamp": "2025-07-05 21:42:27", "type": "API_ERROR", "message": "NASA API error for date 2025-06-05: 429", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 1}, "response": "{\n  \"error\": {\n    \"code\": \"OVER_RATE_LIMIT\",\n    \"message\": \"You have exceeded your rate limit. Try again later or contact us at https://api.nasa.gov:443/contact/ for assistance\"\n  }\n}"}
{"timestamp": "2025-07-05 21:42:28", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=-13.164341, lng=-72.5450094, date=2025-05-06", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 2}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": -13.164341, "lon": -72.5450094, "date": "2025-05-06", "dim": 0.15}}
{"timestamp": "2025-07-05 21:42:28", "type": "API_ERROR", "message": "NASA API error for date 2023-01-01: 429", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 20}, "response": "{\n  \"error\": {\n    \"code\": \"OVER_RATE_LIMIT\",\n    \"message\": \"You have exceeded your rate limit. Try again later or contact us at https://api.nasa.gov:443/contact/ for assistance\"\n  }\n}"}
{"timestamp": "2025-07-05 21:42:28", "type": "RESOURCE", "message": "No valid satellite images available", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 21}, "available_images": ["temp/satellite_single_house.jpg", "temp/satellite_professional_highlighted.jpg", "temp/test_satellite.jpg"]}
{"timestamp": "2025-07-05 21:42:29", "type": "API_ERROR", "message": "NASA API error for date 2025-05-06: 429", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 3}, "response": "{\n  \"error\": {\n    \"code\": \"OVER_RATE_LIMIT\",\n    \"message\": \"You have exceeded your rate limit. Try again later or contact us at https://api.nasa.gov:443/contact/ for assistance\"\n  }\n}"}
{"timestamp": "2025-07-05 21:42:29", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=-13.164341, lng=-72.5450094, date=2025-04-06", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 4}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": -13.164341, "lon": -72.5450094, "date": "2025-04-06", "dim": 0.15}}
{"timestamp": "2025-07-05 21:42:30", "type": "API_ERROR", "message": "NASA API error for date 2025-04-06: 429", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 5}, "response": "{\n  \"error\": {\n    \"code\": \"OVER_RATE_LIMIT\",\n    \"message\": \"You have exceeded your rate limit. Try again later or contact us at https://api.nasa.gov:443/contact/ for assistance\"\n  }\n}"}
{"timestamp": "2025-07-05 21:42:31", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=-13.164341, lng=-72.5450094, date=2023-06-01", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 6}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": -13.164341, "lon": -72.5450094, "date": "2023-06-01", "dim": 0.15}}
{"timestamp": "2025-07-05 21:42:32", "type": "API_ERROR", "message": "NASA API error for date 2023-06-01: 429", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 7}, "response": "{\n  \"error\": {\n    \"code\": \"OVER_RATE_LIMIT\",\n    \"message\": \"You have exceeded your rate limit. Try again later or contact us at https://api.nasa.gov:443/contact/ for assistance\"\n  }\n}"}
{"timestamp": "2025-07-05 21:42:32", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=-13.164341, lng=-72.5450094, date=2023-01-01", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 8}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": -13.164341, "lon": -72.5450094, "date": "2023-01-01", "dim": 0.15}}
{"timestamp": "2025-07-05 21:42:33", "type": "API_ERROR", "message": "NASA API error for date 2023-01-01: 429", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 9}, "response": "{\n  \"error\": {\n    \"code\": \"OVER_RATE_LIMIT\",\n    \"message\": \"You have exceeded your rate limit. Try again later or contact us at https://api.nasa.gov:443/contact/ for assistance\"\n  }\n}"}
{"timestamp": "2025-07-05 21:42:34", "type": "RESOURCE", "message": "No valid satellite images available", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 10}, "available_images": ["temp/satellite_single_house.jpg", "temp/satellite_professional_highlighted.jpg", "temp/test_satellite.jpg"]}
{"timestamp": "2025-07-05 21:42:34", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=-13.164341, lng=-72.5450094, date=2025-06-05", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 11}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": -13.164341, "lon": -72.5450094, "date": "2025-06-05", "dim": 0.15}}
{"timestamp": "2025-07-05 21:42:35", "type": "API_ERROR", "message": "NASA API error for date 2025-06-05: 429", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 12}, "response": "{\n  \"error\": {\n    \"code\": \"OVER_RATE_LIMIT\",\n    \"message\": \"You have exceeded your rate limit. Try again later or contact us at https://api.nasa.gov:443/contact/ for assistance\"\n  }\n}"}
{"timestamp": "2025-07-05 21:42:36", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=-13.164341, lng=-72.5450094, date=2025-05-06", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 13}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": -13.164341, "lon": -72.5450094, "date": "2025-05-06", "dim": 0.15}}
{"timestamp": "2025-07-05 21:42:37", "type": "API_ERROR", "message": "NASA API error for date 2025-05-06: 429", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 14}, "response": "{\n  \"error\": {\n    \"code\": \"OVER_RATE_LIMIT\",\n    \"message\": \"You have exceeded your rate limit. Try again later or contact us at https://api.nasa.gov:443/contact/ for assistance\"\n  }\n}"}
{"timestamp": "2025-07-05 21:42:37", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=-13.164341, lng=-72.5450094, date=2025-04-06", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 15}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": -13.164341, "lon": -72.5450094, "date": "2025-04-06", "dim": 0.15}}
{"timestamp": "2025-07-05 21:42:38", "type": "API_ERROR", "message": "NASA API error for date 2025-04-06: 429", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 16}, "response": "{\n  \"error\": {\n    \"code\": \"OVER_RATE_LIMIT\",\n    \"message\": \"You have exceeded your rate limit. Try again later or contact us at https://api.nasa.gov:443/contact/ for assistance\"\n  }\n}"}
{"timestamp": "2025-07-05 21:42:39", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=-13.164341, lng=-72.5450094, date=2023-06-01", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 17}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": -13.164341, "lon": -72.5450094, "date": "2023-06-01", "dim": 0.15}}
{"timestamp": "2025-07-05 21:42:40", "type": "API_ERROR", "message": "NASA API error for date 2023-06-01: 429", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 18}, "response": "{\n  \"error\": {\n    \"code\": \"OVER_RATE_LIMIT\",\n    \"message\": \"You have exceeded your rate limit. Try again later or contact us at https://api.nasa.gov:443/contact/ for assistance\"\n  }\n}"}
{"timestamp": "2025-07-05 21:42:41", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=-13.164341, lng=-72.5450094, date=2023-01-01", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 19}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": -13.164341, "lon": -72.5450094, "date": "2023-01-01", "dim": 0.15}}
{"timestamp": "2025-07-05 21:42:42", "type": "API_ERROR", "message": "NASA API error for date 2023-01-01: 429", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 20}, "response": "{\n  \"error\": {\n    \"code\": \"OVER_RATE_LIMIT\",\n    \"message\": \"You have exceeded your rate limit. Try again later or contact us at https://api.nasa.gov:443/contact/ for assistance\"\n  }\n}"}
{"timestamp": "2025-07-05 21:42:42", "type": "RESOURCE", "message": "No valid satellite images available", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 21}, "available_images": ["temp/satellite_single_house.jpg", "temp/satellite_professional_highlighted.jpg", "temp/test_satellite.jpg"]}
{"timestamp": "2025-07-05 21:42:52", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=-13.164341, lng=-72.5450094, date=2025-06-05", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": -13.164341, "lon": -72.5450094, "date": "2025-06-05", "dim": 0.15}}
{"timestamp": "2025-07-05 21:42:53", "type": "API_ERROR", "message": "NASA API error for date 2025-06-05: 429", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 1}, "response": "{\n  \"error\": {\n    \"code\": \"OVER_RATE_LIMIT\",\n    \"message\": \"You have exceeded your rate limit. Try again later or contact us at https://api.nasa.gov:443/contact/ for assistance\"\n  }\n}"}
{"timestamp": "2025-07-05 21:42:54", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=-13.164341, lng=-72.5450094, date=2025-05-06", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 2}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": -13.164341, "lon": -72.5450094, "date": "2025-05-06", "dim": 0.15}}
{"timestamp": "2025-07-05 21:42:55", "type": "API_ERROR", "message": "NASA API error for date 2025-05-06: 429", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 3}, "response": "{\n  \"error\": {\n    \"code\": \"OVER_RATE_LIMIT\",\n    \"message\": \"You have exceeded your rate limit. Try again later or contact us at https://api.nasa.gov:443/contact/ for assistance\"\n  }\n}"}
{"timestamp": "2025-07-05 21:42:55", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=-13.164341, lng=-72.5450094, date=2025-04-06", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 4}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": -13.164341, "lon": -72.5450094, "date": "2025-04-06", "dim": 0.15}}
{"timestamp": "2025-07-05 21:42:56", "type": "API_ERROR", "message": "NASA API error for date 2025-04-06: 429", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 5}, "response": "{\n  \"error\": {\n    \"code\": \"OVER_RATE_LIMIT\",\n    \"message\": \"You have exceeded your rate limit. Try again later or contact us at https://api.nasa.gov:443/contact/ for assistance\"\n  }\n}"}
{"timestamp": "2025-07-05 21:42:57", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=-13.164341, lng=-72.5450094, date=2023-06-01", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 6}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": -13.164341, "lon": -72.5450094, "date": "2023-06-01", "dim": 0.15}}
{"timestamp": "2025-07-05 21:42:58", "type": "API_ERROR", "message": "NASA API error for date 2023-06-01: 429", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 7}, "response": "{\n  \"error\": {\n    \"code\": \"OVER_RATE_LIMIT\",\n    \"message\": \"You have exceeded your rate limit. Try again later or contact us at https://api.nasa.gov:443/contact/ for assistance\"\n  }\n}"}
{"timestamp": "2025-07-05 21:42:58", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=-13.164341, lng=-72.5450094, date=2023-01-01", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 8}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": -13.164341, "lon": -72.5450094, "date": "2023-01-01", "dim": 0.15}}
{"timestamp": "2025-07-05 21:43:00", "type": "API_ERROR", "message": "NASA API error for date 2023-01-01: 429", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 9}, "response": "{\n  \"error\": {\n    \"code\": \"OVER_RATE_LIMIT\",\n    \"message\": \"You have exceeded your rate limit. Try again later or contact us at https://api.nasa.gov:443/contact/ for assistance\"\n  }\n}"}
{"timestamp": "2025-07-05 21:43:00", "type": "RESOURCE", "message": "No valid satellite images available", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 10}, "available_images": ["temp/satellite_single_house.jpg", "temp/satellite_professional_highlighted.jpg", "temp/test_satellite.jpg"]}
{"timestamp": "2025-07-05 21:43:00", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=-13.164341, lng=-72.5450094, date=2025-06-05", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 11}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": -13.164341, "lon": -72.5450094, "date": "2025-06-05", "dim": 0.15}}
{"timestamp": "2025-07-05 21:43:01", "type": "API_ERROR", "message": "NASA API error for date 2025-06-05: 429", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 12}, "response": "{\n  \"error\": {\n    \"code\": \"OVER_RATE_LIMIT\",\n    \"message\": \"You have exceeded your rate limit. Try again later or contact us at https://api.nasa.gov:443/contact/ for assistance\"\n  }\n}"}
{"timestamp": "2025-07-05 21:43:02", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=-13.164341, lng=-72.5450094, date=2025-05-06", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 13}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": -13.164341, "lon": -72.5450094, "date": "2025-05-06", "dim": 0.15}}
{"timestamp": "2025-07-05 21:43:03", "type": "API_ERROR", "message": "NASA API error for date 2025-05-06: 429", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 14}, "response": "{\n  \"error\": {\n    \"code\": \"OVER_RATE_LIMIT\",\n    \"message\": \"You have exceeded your rate limit. Try again later or contact us at https://api.nasa.gov:443/contact/ for assistance\"\n  }\n}"}
{"timestamp": "2025-07-05 21:43:03", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=-13.164341, lng=-72.5450094, date=2025-04-06", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 15}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": -13.164341, "lon": -72.5450094, "date": "2025-04-06", "dim": 0.15}}
{"timestamp": "2025-07-05 21:43:04", "type": "API_ERROR", "message": "NASA API error for date 2025-04-06: 429", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 16}, "response": "{\n  \"error\": {\n    \"code\": \"OVER_RATE_LIMIT\",\n    \"message\": \"You have exceeded your rate limit. Try again later or contact us at https://api.nasa.gov:443/contact/ for assistance\"\n  }\n}"}
{"timestamp": "2025-07-05 21:43:05", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=-13.164341, lng=-72.5450094, date=2023-06-01", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 17}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": -13.164341, "lon": -72.5450094, "date": "2023-06-01", "dim": 0.15}}
{"timestamp": "2025-07-05 21:43:06", "type": "API_ERROR", "message": "NASA API error for date 2023-06-01: 429", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 18}, "response": "{\n  \"error\": {\n    \"code\": \"OVER_RATE_LIMIT\",\n    \"message\": \"You have exceeded your rate limit. Try again later or contact us at https://api.nasa.gov:443/contact/ for assistance\"\n  }\n}"}
{"timestamp": "2025-07-05 21:43:06", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=-13.164341, lng=-72.5450094, date=2023-01-01", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 19}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": -13.164341, "lon": -72.5450094, "date": "2023-01-01", "dim": 0.15}}
{"timestamp": "2025-07-05 21:43:08", "type": "API_ERROR", "message": "NASA API error for date 2023-01-01: 429", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 20}, "response": "{\n  \"error\": {\n    \"code\": \"OVER_RATE_LIMIT\",\n    \"message\": \"You have exceeded your rate limit. Try again later or contact us at https://api.nasa.gov:443/contact/ for assistance\"\n  }\n}"}
{"timestamp": "2025-07-05 21:43:08", "type": "RESOURCE", "message": "No valid satellite images available", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 21}, "available_images": ["temp/satellite_single_house.jpg", "temp/satellite_professional_highlighted.jpg", "temp/test_satellite.jpg"]}
{"timestamp": "2025-07-05 21:43:19", "type": "NO_RESULTS", "message": "No results from Nominatim for: rinconda library", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}}
{"timestamp": "2025-07-05 21:43:19", "type": "ALTERNATIVE", "message": "Trying alternative format: rinconda library, USA", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 1}}
{"timestamp": "2025-07-05 21:43:20", "type": "NO_RESULTS", "message": "No results from Nominatim for: rinconda library, USA", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 2}}
{"timestamp": "2025-07-05 21:55:59", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=40.713451, lng=-73.9914854, date=2025-06-05", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 40.713451, "lon": -73.9914854, "date": "2025-06-05", "dim": 0.15}}
{"timestamp": "2025-07-05 21:56:00", "type": "API_ERROR", "message": "NASA API error for date 2025-06-05: 429", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 1}, "response": "{\n  \"error\": {\n    \"code\": \"OVER_RATE_LIMIT\",\n    \"message\": \"You have exceeded your rate limit. Try again later or contact us at https://api.nasa.gov:443/contact/ for assistance\"\n  }\n}"}
{"timestamp": "2025-07-05 21:56:01", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=40.713451, lng=-73.9914854, date=2025-05-06", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 2}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 40.713451, "lon": -73.9914854, "date": "2025-05-06", "dim": 0.15}}
{"timestamp": "2025-07-05 21:56:02", "type": "API_ERROR", "message": "NASA API error for date 2025-05-06: 429", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 3}, "response": "{\n  \"error\": {\n    \"code\": \"OVER_RATE_LIMIT\",\n    \"message\": \"You have exceeded your rate limit. Try again later or contact us at https://api.nasa.gov:443/contact/ for assistance\"\n  }\n}"}
{"timestamp": "2025-07-05 21:56:02", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=40.713451, lng=-73.9914854, date=2025-04-06", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 4}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 40.713451, "lon": -73.9914854, "date": "2025-04-06", "dim": 0.15}}
{"timestamp": "2025-07-05 21:56:04", "type": "API_ERROR", "message": "NASA API error for date 2025-04-06: 429", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 5}, "response": "{\n  \"error\": {\n    \"code\": \"OVER_RATE_LIMIT\",\n    \"message\": \"You have exceeded your rate limit. Try again later or contact us at https://api.nasa.gov:443/contact/ for assistance\"\n  }\n}"}
{"timestamp": "2025-07-05 21:56:04", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=40.713451, lng=-73.9914854, date=2023-06-01", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 6}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 40.713451, "lon": -73.9914854, "date": "2023-06-01", "dim": 0.15}}
{"timestamp": "2025-07-05 21:56:05", "type": "API_ERROR", "message": "NASA API error for date 2023-06-01: 429", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 7}, "response": "{\n  \"error\": {\n    \"code\": \"OVER_RATE_LIMIT\",\n    \"message\": \"You have exceeded your rate limit. Try again later or contact us at https://api.nasa.gov:443/contact/ for assistance\"\n  }\n}"}
{"timestamp": "2025-07-05 21:56:05", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=40.713451, lng=-73.9914854, date=2025-06-05", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 40.713451, "lon": -73.9914854, "date": "2025-06-05", "dim": 0.15}}
{"timestamp": "2025-07-05 21:56:06", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=40.713451, lng=-73.9914854, date=2023-01-01", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 8}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 40.713451, "lon": -73.9914854, "date": "2023-01-01", "dim": 0.15}}
{"timestamp": "2025-07-05 21:56:06", "type": "API_ERROR", "message": "NASA API error for date 2025-06-05: 429", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 1}, "response": "{\n  \"error\": {\n    \"code\": \"OVER_RATE_LIMIT\",\n    \"message\": \"You have exceeded your rate limit. Try again later or contact us at https://api.nasa.gov:443/contact/ for assistance\"\n  }\n}"}
{"timestamp": "2025-07-05 21:56:07", "type": "API_ERROR", "message": "NASA API error for date 2023-01-01: 429", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 9}, "response": "{\n  \"error\": {\n    \"code\": \"OVER_RATE_LIMIT\",\n    \"message\": \"You have exceeded your rate limit. Try again later or contact us at https://api.nasa.gov:443/contact/ for assistance\"\n  }\n}"}
{"timestamp": "2025-07-05 21:56:07", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=40.713451, lng=-73.9914854, date=2025-05-06", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 2}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 40.713451, "lon": -73.9914854, "date": "2025-05-06", "dim": 0.15}}
{"timestamp": "2025-07-05 21:56:07", "type": "RESOURCE", "message": "No valid satellite images available", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 10}, "available_images": ["temp/satellite_single_house.jpg", "temp/satellite_professional_highlighted.jpg", "temp/test_satellite.jpg"]}
{"timestamp": "2025-07-05 21:56:08", "type": "API_ERROR", "message": "NASA API error for date 2025-05-06: 429", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 3}, "response": "{\n  \"error\": {\n    \"code\": \"OVER_RATE_LIMIT\",\n    \"message\": \"You have exceeded your rate limit. Try again later or contact us at https://api.nasa.gov:443/contact/ for assistance\"\n  }\n}"}
{"timestamp": "2025-07-05 21:56:08", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=40.713451, lng=-73.9914854, date=2025-04-06", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 4}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 40.713451, "lon": -73.9914854, "date": "2025-04-06", "dim": 0.15}}
{"timestamp": "2025-07-05 21:56:10", "type": "API_ERROR", "message": "NASA API error for date 2025-04-06: 429", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 5}, "response": "{\n  \"error\": {\n    \"code\": \"OVER_RATE_LIMIT\",\n    \"message\": \"You have exceeded your rate limit. Try again later or contact us at https://api.nasa.gov:443/contact/ for assistance\"\n  }\n}"}
{"timestamp": "2025-07-05 21:56:10", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=40.713451, lng=-73.9914854, date=2023-06-01", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 6}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 40.713451, "lon": -73.9914854, "date": "2023-06-01", "dim": 0.15}}
{"timestamp": "2025-07-05 21:56:11", "type": "API_ERROR", "message": "NASA API error for date 2023-06-01: 429", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 7}, "response": "{\n  \"error\": {\n    \"code\": \"OVER_RATE_LIMIT\",\n    \"message\": \"You have exceeded your rate limit. Try again later or contact us at https://api.nasa.gov:443/contact/ for assistance\"\n  }\n}"}
{"timestamp": "2025-07-05 21:56:12", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=40.713451, lng=-73.9914854, date=2023-01-01", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 8}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 40.713451, "lon": -73.9914854, "date": "2023-01-01", "dim": 0.15}}
{"timestamp": "2025-07-05 21:56:13", "type": "API_ERROR", "message": "NASA API error for date 2023-01-01: 429", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 9}, "response": "{\n  \"error\": {\n    \"code\": \"OVER_RATE_LIMIT\",\n    \"message\": \"You have exceeded your rate limit. Try again later or contact us at https://api.nasa.gov:443/contact/ for assistance\"\n  }\n}"}
{"timestamp": "2025-07-05 21:56:13", "type": "RESOURCE", "message": "No valid satellite images available", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 10}, "available_images": ["temp/satellite_single_house.jpg", "temp/satellite_professional_highlighted.jpg", "temp/test_satellite.jpg"]}
{"timestamp": "2025-07-05 21:56:13", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=40.713451, lng=-73.9914854, date=2025-06-05", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 11}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 40.713451, "lon": -73.9914854, "date": "2025-06-05", "dim": 0.15}}
{"timestamp": "2025-07-05 21:56:14", "type": "API_ERROR", "message": "NASA API error for date 2025-06-05: 429", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 12}, "response": "{\n  \"error\": {\n    \"code\": \"OVER_RATE_LIMIT\",\n    \"message\": \"You have exceeded your rate limit. Try again later or contact us at https://api.nasa.gov:443/contact/ for assistance\"\n  }\n}"}
{"timestamp": "2025-07-05 21:56:15", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=40.713451, lng=-73.9914854, date=2025-05-06", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 13}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 40.713451, "lon": -73.9914854, "date": "2025-05-06", "dim": 0.15}}
{"timestamp": "2025-07-05 21:56:16", "type": "API_ERROR", "message": "NASA API error for date 2025-05-06: 429", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 14}, "response": "{\n  \"error\": {\n    \"code\": \"OVER_RATE_LIMIT\",\n    \"message\": \"You have exceeded your rate limit. Try again later or contact us at https://api.nasa.gov:443/contact/ for assistance\"\n  }\n}"}
{"timestamp": "2025-07-05 21:56:17", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=40.713451, lng=-73.9914854, date=2025-04-06", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 15}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 40.713451, "lon": -73.9914854, "date": "2025-04-06", "dim": 0.15}}
{"timestamp": "2025-07-05 21:56:18", "type": "API_ERROR", "message": "NASA API error for date 2025-04-06: 429", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 16}, "response": "{\n  \"error\": {\n    \"code\": \"OVER_RATE_LIMIT\",\n    \"message\": \"You have exceeded your rate limit. Try again later or contact us at https://api.nasa.gov:443/contact/ for assistance\"\n  }\n}"}
{"timestamp": "2025-07-05 21:56:18", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=40.713451, lng=-73.9914854, date=2023-06-01", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 17}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 40.713451, "lon": -73.9914854, "date": "2023-06-01", "dim": 0.15}}
{"timestamp": "2025-07-05 21:56:19", "type": "API_ERROR", "message": "NASA API error for date 2023-06-01: 429", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 18}, "response": "{\n  \"error\": {\n    \"code\": \"OVER_RATE_LIMIT\",\n    \"message\": \"You have exceeded your rate limit. Try again later or contact us at https://api.nasa.gov:443/contact/ for assistance\"\n  }\n}"}
{"timestamp": "2025-07-05 21:56:20", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=40.713451, lng=-73.9914854, date=2023-01-01", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 19}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 40.713451, "lon": -73.9914854, "date": "2023-01-01", "dim": 0.15}}
{"timestamp": "2025-07-05 21:56:21", "type": "API_ERROR", "message": "NASA API error for date 2023-01-01: 429", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 20}, "response": "{\n  \"error\": {\n    \"code\": \"OVER_RATE_LIMIT\",\n    \"message\": \"You have exceeded your rate limit. Try again later or contact us at https://api.nasa.gov:443/contact/ for assistance\"\n  }\n}"}
{"timestamp": "2025-07-05 21:56:21", "type": "RESOURCE", "message": "No valid satellite images available", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 21}, "available_images": ["temp/satellite_single_house.jpg", "temp/satellite_professional_highlighted.jpg", "temp/test_satellite.jpg"]}
