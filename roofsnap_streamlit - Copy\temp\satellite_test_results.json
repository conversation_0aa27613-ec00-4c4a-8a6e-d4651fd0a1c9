{"pipeline": {"successful_retrievals": 5, "failed_retrievals": 1, "fallback_usage": 5, "nasa_api_usage": 0, "error_types": {"coordinate_validation": 1}, "image_details": [{"location": "New York City", "path": "temp/satellite_single_house.jpg", "size": 88382, "dimensions": "640x640", "format": "JPEG", "source": "Test image fallback"}, {"location": "Los Angeles", "path": "temp/satellite_single_house.jpg", "size": 88382, "dimensions": "640x640", "format": "JPEG", "source": "Test image fallback"}, {"location": "London, UK", "path": "temp/satellite_single_house.jpg", "size": 88382, "dimensions": "640x640", "format": "JPEG", "source": "Test image fallback"}, {"location": "Tokyo, Japan", "path": "temp/satellite_single_house.jpg", "size": 88382, "dimensions": "640x640", "format": "JPEG", "source": "Test image fallback"}, {"location": "Sydney, Australia", "path": "temp/satellite_single_house.jpg", "size": 88382, "dimensions": "640x640", "format": "JPEG", "source": "Test image fallback"}]}, "fallback": {"available_images": 3, "valid_images": 3, "image_details": [{"path": "temp/satellite_single_house.jpg", "size": 88382, "dimensions": "640x640", "format": "JPEG", "mode": "RGB", "valid": true}, {"path": "temp/satellite_professional_highlighted.jpg", "size": 179815, "dimensions": "640x640", "format": "JPEG", "mode": "RGB", "valid": true}, {"path": "temp/test_satellite.jpg", "size": 11061, "dimensions": "800x800", "format": "JPEG", "mode": "RGB", "valid": true}]}, "edge_cases": {"handled_correctly": 7, "unexpected_behavior": 0, "error_messages": [{"case": "Invalid latitude (too high)", "error": "Invalid coordinates: latitude must be between -90 and 90, longitude between -180 and 180"}, {"case": "Invalid latitude (too low)", "error": "Invalid coordinates: latitude must be between -90 and 90, longitude between -180 and 180"}, {"case": "Invalid longitude (too high)", "error": "Invalid coordinates: latitude must be between -90 and 90, longitude between -180 and 180"}, {"case": "Invalid longitude (too low)", "error": "Invalid coordinates: latitude must be between -90 and 90, longitude between -180 and 180"}, {"case": "String coordinates", "error": "Coordinates must be numbers"}, {"case": "None coordinates", "error": "Coordinates must be numbers"}]}, "summary": {"success_rate": 83.33333333333334, "fallback_reliable": true, "error_handling_robust": true}}