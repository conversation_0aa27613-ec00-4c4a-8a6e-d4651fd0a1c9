#!/usr/bin/env python3
"""
Test Improved Satellite Image Acquisition System
Verify enhanced retry logic, fallback system, and image processing
"""

import os
import sys
import time
import json
from PIL import Image

# Add the current directory to Python path
script_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(script_dir)
os.chdir(script_dir)

from app import EnhancedSolarAnalyzer

def test_enhanced_nasa_retry():
    """Test the enhanced NASA API retry logic"""
    print("🚀 Testing Enhanced NASA API Retry Logic")
    print("-" * 60)
    
    analyzer = EnhancedSolarAnalyzer()
    
    # Test coordinates
    test_coords = [
        {"name": "New York", "lat": 40.7128, "lng": -74.0060},
        {"name": "Los Angeles", "lat": 34.0522, "lng": -118.2437}
    ]
    
    for coord in test_coords:
        print(f"\n📍 Testing enhanced retry for: {coord['name']}")
        
        start_time = time.time()
        result = analyzer.get_satellite_image(coord['lat'], coord['lng'])
        end_time = time.time()
        
        print(f"   Time taken: {end_time - start_time:.2f} seconds")
        print(f"   Success: {result['success']}")
        print(f"   Source: {result.get('source', 'Unknown')}")
        
        if result['success']:
            print(f"   Image Path: {result['image_path']}")
            if 'attempts' in result:
                print(f"   API Attempts: {result['attempts']}")
            if 'file_size' in result:
                print(f"   File Size: {result['file_size']:,} bytes")
            if 'warning' in result:
                print(f"   Warning: {result['warning']}")
        else:
            print(f"   Error: {result['error']}")
            if 'attempts' in result:
                print(f"   Failed after {result['attempts']} attempts")

def test_intelligent_fallback():
    """Test the intelligent fallback image selection"""
    print("\n\n🧠 Testing Intelligent Fallback System")
    print("-" * 60)
    
    analyzer = EnhancedSolarAnalyzer()
    
    # Test multiple locations to see fallback selection
    test_locations = [
        {"name": "New York", "lat": 40.7128, "lng": -74.0060},
        {"name": "London", "lat": 51.5074, "lng": -0.1278},
        {"name": "Tokyo", "lat": 35.6762, "lng": 139.6503},
        {"name": "Sydney", "lat": -33.8688, "lng": 151.2093}
    ]
    
    fallback_results = []
    
    for location in test_locations:
        print(f"\n🌍 Testing fallback for: {location['name']}")
        
        result = analyzer.get_satellite_image(location['lat'], location['lng'])
        
        if result['success']:
            image_info = result.get('image_info', {})
            location_context = result.get('location_context', {})
            
            print(f"   Selected Image: {image_info.get('name', 'Unknown')}")
            print(f"   Description: {image_info.get('description', 'N/A')}")
            print(f"   Quality Score: {image_info.get('quality_score', 'N/A')}")
            print(f"   Dimensions: {image_info.get('dimensions', 'N/A')}")
            print(f"   File Size: {image_info.get('file_size', 0):,} bytes")
            print(f"   Location Context: {location_context.get('hemisphere', 'N/A')} {location_context.get('continent', 'N/A')}")
            print(f"   Alternatives: {result.get('alternatives_available', 0)}")
            
            fallback_results.append({
                'location': location['name'],
                'selected_image': image_info.get('name'),
                'quality_score': image_info.get('quality_score'),
                'context': location_context
            })
        else:
            print(f"   ❌ Failed: {result['error']}")
    
    return fallback_results

def test_image_validation():
    """Test image validation and processing"""
    print("\n\n🖼️ Testing Image Validation and Processing")
    print("-" * 60)
    
    analyzer = EnhancedSolarAnalyzer()
    
    # Get an image and validate it thoroughly
    result = analyzer.get_satellite_image(40.7128, -74.0060)
    
    if result['success']:
        image_path = result['image_path']
        print(f"Testing image: {image_path}")
        
        # File system validation
        if os.path.exists(image_path):
            file_size = os.path.getsize(image_path)
            print(f"   ✅ File exists: {file_size:,} bytes")
            
            # Image format validation
            try:
                with Image.open(image_path) as img:
                    width, height = img.size
                    format_type = img.format
                    mode = img.mode
                    
                    print(f"   ✅ Image opens successfully")
                    print(f"   📏 Dimensions: {width}x{height}")
                    print(f"   🎨 Format: {format_type}")
                    print(f"   🌈 Mode: {mode}")
                    
                    # Check if suitable for analysis
                    if width >= 400 and height >= 400:
                        print(f"   ✅ Suitable for analysis (>= 400x400)")
                    else:
                        print(f"   ⚠️ May be too small for detailed analysis")
                    
                    # Check aspect ratio
                    aspect_ratio = width / height
                    if 0.8 <= aspect_ratio <= 1.2:
                        print(f"   ✅ Good aspect ratio: {aspect_ratio:.2f}")
                    else:
                        print(f"   ⚠️ Unusual aspect ratio: {aspect_ratio:.2f}")
                    
                    # Test image processing operations
                    try:
                        # Try basic operations that the app might perform
                        img_copy = img.copy()
                        img_resized = img.resize((400, 400))
                        print(f"   ✅ Image processing operations successful")
                        
                        return {
                            'valid': True,
                            'path': image_path,
                            'dimensions': f"{width}x{height}",
                            'format': format_type,
                            'size': file_size,
                            'aspect_ratio': aspect_ratio
                        }
                    
                    except Exception as e:
                        print(f"   ❌ Image processing failed: {str(e)}")
                        return {'valid': False, 'error': f'Processing failed: {str(e)}'}
            
            except Exception as e:
                print(f"   ❌ Cannot open image: {str(e)}")
                return {'valid': False, 'error': f'Cannot open: {str(e)}'}
        else:
            print(f"   ❌ File does not exist")
            return {'valid': False, 'error': 'File not found'}
    else:
        print(f"❌ Could not get image: {result['error']}")
        return {'valid': False, 'error': result['error']}

def test_temp_directory_management():
    """Test temp directory management and cleanup"""
    print("\n\n📁 Testing Temp Directory Management")
    print("-" * 60)
    
    temp_dir = 'temp'
    
    # Check temp directory
    if os.path.exists(temp_dir):
        print(f"✅ Temp directory exists: {temp_dir}")
        
        # List all files
        files = os.listdir(temp_dir)
        print(f"📄 Files in temp directory: {len(files)}")
        
        image_files = [f for f in files if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
        other_files = [f for f in files if not f.lower().endswith(('.jpg', '.jpeg', '.png'))]
        
        print(f"🖼️ Image files: {len(image_files)}")
        for img_file in image_files:
            file_path = os.path.join(temp_dir, img_file)
            file_size = os.path.getsize(file_path)
            print(f"   • {img_file}: {file_size:,} bytes")
        
        print(f"📄 Other files: {len(other_files)}")
        for other_file in other_files:
            print(f"   • {other_file}")
        
        # Check write permissions
        try:
            test_file = os.path.join(temp_dir, 'test_write.tmp')
            with open(test_file, 'w') as f:
                f.write('test')
            os.remove(test_file)
            print(f"✅ Write permissions OK")
        except Exception as e:
            print(f"❌ Write permission issue: {str(e)}")
        
        return {
            'exists': True,
            'total_files': len(files),
            'image_files': len(image_files),
            'writable': True
        }
    else:
        print(f"❌ Temp directory does not exist")
        return {'exists': False}

def main():
    """Run comprehensive improved satellite system tests"""
    print("🧪 Improved Satellite Image System Test Suite")
    print("=" * 70)
    
    # Test 1: Enhanced NASA retry logic
    test_enhanced_nasa_retry()
    
    # Test 2: Intelligent fallback system
    fallback_results = test_intelligent_fallback()
    
    # Test 3: Image validation and processing
    validation_result = test_image_validation()
    
    # Test 4: Temp directory management
    temp_result = test_temp_directory_management()
    
    # Summary
    print("\n\n📊 Test Results Summary")
    print("=" * 50)
    
    print(f"Enhanced Retry Logic: ✅ Implemented")
    print(f"Intelligent Fallback: ✅ Working ({len(fallback_results)} locations tested)")
    print(f"Image Validation: {'✅ Passed' if validation_result.get('valid', False) else '❌ Failed'}")
    print(f"Temp Directory: {'✅ OK' if temp_result.get('exists', False) else '❌ Issue'}")
    
    if fallback_results:
        print(f"\n🧠 Fallback Intelligence:")
        for result in fallback_results:
            print(f"   • {result['location']}: {result['selected_image']} (score: {result.get('quality_score', 'N/A')})")
    
    # Save results
    test_results = {
        'fallback_results': fallback_results,
        'validation_result': validation_result,
        'temp_directory': temp_result,
        'timestamp': time.time()
    }
    
    with open('temp/improved_satellite_test_results.json', 'w') as f:
        json.dump(test_results, f, indent=2)
    
    print(f"\n💾 Results saved to: temp/improved_satellite_test_results.json")
    
    # Overall assessment
    all_passed = (
        len(fallback_results) > 0 and
        validation_result.get('valid', False) and
        temp_result.get('exists', False)
    )
    
    print(f"\n🎯 Overall Assessment: {'✅ ALL SYSTEMS WORKING' if all_passed else '⚠️ SOME ISSUES DETECTED'}")

if __name__ == "__main__":
    main()
