#!/usr/bin/env python3
"""
Test NASA API Integration
Test the NASA Earth Imagery API and other components
"""

import os
import sys
import requests
from datetime import datetime, timedelta

# Add the current directory to Python path and change to correct directory
script_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(script_dir)
os.chdir(script_dir)

from app import EnhancedSolarAnalyzer

def test_nasa_api_direct():
    """Test NASA API directly"""
    print("🚀 Testing NASA Earth Imagery API directly...")
    
    url = "https://api.nasa.gov/planetary/earth/imagery"
    
    # Test coordinates (Los Angeles)
    lat, lng = 34.0522, -118.2437
    date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
    
    params = {
        'lat': lat,
        'lon': lng,
        'date': date,
        'dim': 0.1,
        'api_key': 'DEMO_KEY'
    }
    
    print(f"📍 Testing coordinates: {lat}, {lng}")
    print(f"📅 Testing date: {date}")
    print(f"🔗 Request URL: {url}")
    print(f"📋 Parameters: {params}")
    
    try:
        response = requests.get(url, params=params, timeout=30)
        print(f"📊 Response status: {response.status_code}")
        print(f"📄 Response headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            content_type = response.headers.get('content-type', '')
            print(f"📝 Content type: {content_type}")
            print(f"📏 Content length: {len(response.content)} bytes")
            
            if 'image' in content_type:
                print("✅ Successfully received image from NASA API!")
                return True
            else:
                print(f"❌ Received non-image content: {response.text[:200]}")
                return False
        else:
            print(f"❌ API error: {response.status_code}")
            print(f"📄 Response: {response.text[:200]}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {str(e)}")
        return False

def test_analyzer_integration():
    """Test the EnhancedSolarAnalyzer class"""
    print("\n🏠 Testing EnhancedSolarAnalyzer integration...")
    
    try:
        analyzer = EnhancedSolarAnalyzer()
        print("✅ Analyzer initialized successfully")
        
        # Test geocoding
        print("\n📍 Testing geocoding...")
        address = "1600 Amphitheatre Parkway, Mountain View, CA"
        geocode_result = analyzer.geocode_address(address)
        print(f"Geocoding result: {geocode_result}")
        
        if geocode_result['success']:
            lat, lng = geocode_result['lat'], geocode_result['lng']
            print(f"✅ Geocoding successful: {lat}, {lng}")
            
            # Test satellite image retrieval
            print("\n🛰️ Testing satellite image retrieval...")
            sat_result = analyzer.get_satellite_image(lat, lng)
            print(f"Satellite result: {sat_result}")
            
            if sat_result['success']:
                print("✅ Satellite image retrieval successful!")
                return True
            else:
                print(f"❌ Satellite image failed: {sat_result.get('error', 'Unknown error')}")
                return False
        else:
            print(f"❌ Geocoding failed: {geocode_result.get('error', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ Analyzer test failed: {str(e)}")
        return False

def main():
    """Run all tests"""
    print("🧪 NASA API Integration Test Suite")
    print("=" * 50)
    
    # Test 1: Direct NASA API
    nasa_direct = test_nasa_api_direct()
    
    # Test 2: Analyzer integration
    analyzer_test = test_analyzer_integration()
    
    # Summary
    print("\n📊 Test Results Summary")
    print("=" * 30)
    print(f"NASA API Direct: {'✅ PASS' if nasa_direct else '❌ FAIL'}")
    print(f"Analyzer Integration: {'✅ PASS' if analyzer_test else '❌ FAIL'}")
    
    if nasa_direct and analyzer_test:
        print("\n🎉 All tests passed! NASA integration is working.")
    elif not nasa_direct and analyzer_test:
        print("\n⚠️ NASA API may have limitations with DEMO_KEY, but fallback system works.")
    else:
        print("\n❌ Some tests failed. Check the error messages above.")

if __name__ == "__main__":
    main()
