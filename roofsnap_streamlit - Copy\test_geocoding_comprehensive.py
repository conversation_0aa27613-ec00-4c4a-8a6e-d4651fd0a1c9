#!/usr/bin/env python3
"""
Comprehensive Geocoding Test Suite
Test various address formats, edge cases, and error conditions
"""

import os
import sys
import time
import json

# Add the current directory to Python path
script_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(script_dir)
os.chdir(script_dir)

from app import EnhancedSolarAnalyzer

def test_address_formats():
    """Test various address formats"""
    print("🏠 Testing Various Address Formats")
    print("-" * 40)
    
    analyzer = EnhancedSolarAnalyzer()
    
    test_cases = [
        # Full addresses (should work)
        {
            'address': '1600 Pennsylvania Avenue NW, Washington, DC 20500',
            'expected': True,
            'description': 'Full US address with ZIP'
        },
        {
            'address': '350 Fifth Avenue, New York, NY 10118',
            'expected': True,
            'description': 'Empire State Building'
        },
        {
            'address': '1 Infinite Loop, Cupertino, CA 95014',
            'expected': True,
            'description': 'Apple headquarters'
        },
        
        # Partial addresses (should work)
        {
            'address': 'Times Square, New York',
            'expected': True,
            'description': 'Famous landmark'
        },
        {
            'address': 'Central Park, NYC',
            'expected': True,
            'description': 'Park with city abbreviation'
        },
        {
            'address': 'Golden Gate Bridge, San Francisco',
            'expected': True,
            'description': 'Famous bridge'
        },
        
        # International addresses (should work)
        {
            'address': '10 Downing Street, London, UK',
            'expected': True,
            'description': 'UK address'
        },
        {
            'address': 'Champs-Élysées, Paris, France',
            'expected': True,
            'description': 'French address'
        },
        {
            'address': 'Sydney Opera House, Australia',
            'expected': True,
            'description': 'Australian landmark'
        },
        
        # City/State only (should work)
        {
            'address': 'Los Angeles, CA',
            'expected': True,
            'description': 'City and state'
        },
        {
            'address': 'Miami, Florida',
            'expected': True,
            'description': 'City and full state name'
        },
        
        # Edge cases (may fail)
        {
            'address': '123 Fake Street, Nowhere, XX',
            'expected': False,
            'description': 'Non-existent address'
        },
        {
            'address': 'Atlantis, Lost City',
            'expected': False,
            'description': 'Fictional location'
        },
        {
            'address': '',
            'expected': False,
            'description': 'Empty string'
        },
        {
            'address': '   ',
            'expected': False,
            'description': 'Whitespace only'
        },
        {
            'address': '12345',
            'expected': False,
            'description': 'Numbers only'
        }
    ]
    
    results = []
    
    for i, test_case in enumerate(test_cases, 1):
        address = test_case['address']
        expected = test_case['expected']
        description = test_case['description']
        
        print(f"\n{i:2d}. Testing: {description}")
        print(f"    Address: '{address}'")
        
        try:
            result = analyzer.geocode_address(address)
            success = result['success']
            
            if success:
                lat, lng = result['lat'], result['lng']
                formatted = result['formatted_address']
                print(f"    ✅ SUCCESS: {lat:.6f}, {lng:.6f}")
                print(f"    📍 Formatted: {formatted[:80]}...")
            else:
                error = result.get('error', 'Unknown error')
                print(f"    ❌ FAILED: {error}")
            
            # Check if result matches expectation
            if success == expected:
                status = "✅ EXPECTED"
            else:
                status = "⚠️ UNEXPECTED"
            
            print(f"    {status} (Expected: {'Success' if expected else 'Failure'})")
            
            results.append({
                'address': address,
                'description': description,
                'expected': expected,
                'actual': success,
                'correct': success == expected,
                'result': result
            })
            
        except Exception as e:
            print(f"    💥 EXCEPTION: {str(e)}")
            results.append({
                'address': address,
                'description': description,
                'expected': expected,
                'actual': False,
                'correct': False,
                'result': {'success': False, 'error': f'Exception: {str(e)}'}
            })
        
        # Be respectful to the API
        time.sleep(0.5)
    
    return results

def test_error_handling():
    """Test error handling scenarios"""
    print("\n\n🚨 Testing Error Handling")
    print("-" * 40)
    
    analyzer = EnhancedSolarAnalyzer()
    
    error_cases = [
        None,
        123,
        [],
        {},
        True,
        False
    ]
    
    for case in error_cases:
        print(f"\nTesting invalid input: {type(case).__name__} - {case}")
        try:
            result = analyzer.geocode_address(case)
            print(f"   Result: {result}")
        except Exception as e:
            print(f"   Exception: {str(e)}")

def analyze_results(results):
    """Analyze test results and provide insights"""
    print("\n\n📊 Test Results Analysis")
    print("=" * 50)
    
    total = len(results)
    correct = sum(1 for r in results if r['correct'])
    successful = sum(1 for r in results if r['actual'])
    
    print(f"Total tests: {total}")
    print(f"Correct predictions: {correct}/{total} ({correct/total*100:.1f}%)")
    print(f"Successful geocoding: {successful}/{total} ({successful/total*100:.1f}%)")
    
    print("\n✅ Successful Geocoding:")
    for r in results:
        if r['actual']:
            print(f"   • {r['description']}: '{r['address']}'")
    
    print("\n❌ Failed Geocoding:")
    for r in results:
        if not r['actual']:
            error = r['result'].get('error', 'Unknown error')
            print(f"   • {r['description']}: '{r['address']}' - {error}")
    
    print("\n⚠️ Unexpected Results:")
    for r in results:
        if not r['correct']:
            expected_str = "Success" if r['expected'] else "Failure"
            actual_str = "Success" if r['actual'] else "Failure"
            print(f"   • {r['description']}: Expected {expected_str}, got {actual_str}")

def main():
    """Run comprehensive geocoding tests"""
    print("🧪 Comprehensive Geocoding Test Suite")
    print("=" * 50)
    
    # Test various address formats
    results = test_address_formats()
    
    # Test error handling
    test_error_handling()
    
    # Analyze results
    analyze_results(results)
    
    # Save results to file
    with open('temp/geocoding_test_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\n💾 Results saved to: temp/geocoding_test_results.json")

if __name__ == "__main__":
    main()
