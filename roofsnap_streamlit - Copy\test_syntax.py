#!/usr/bin/env python3
"""
Test syntax of app.py file
"""

import ast
import sys

def check_syntax(filename):
    """Check syntax of Python file"""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            source = f.read()
        
        # Try to parse the AST
        ast.parse(source, filename=filename)
        print(f"✅ Syntax is valid for {filename}")
        return True
        
    except SyntaxError as e:
        print(f"❌ Syntax Error in {filename}:")
        print(f"   Line {e.lineno}: {e.text.strip() if e.text else 'N/A'}")
        print(f"   Error: {e.msg}")
        print(f"   Position: {' ' * (e.offset - 1) if e.offset else ''}^")
        return False
    
    except Exception as e:
        print(f"❌ Other error in {filename}: {str(e)}")
        return False

if __name__ == "__main__":
    filename = "roofsnap_streamlit - Copy/app.py"
    if check_syntax(filename):
        print("🎉 File is ready to run!")
    else:
        print("🔧 Please fix the syntax errors before running.")
