"""
Simple Solar Roof Analyzer
A focused, single-house solar analysis tool using Google APIs and Gemini AI

Required APIs:
- Google Maps Geocoding API
- Google Maps Static Maps API  
- Google Street View API
- Google Solar API
- Google Maps Elevation API
- Google Vision API
- Google Gemini 1.5 Flash
"""

import streamlit as st
import requests
import base64
import json
import os
from PIL import Image, ImageDraw, ImageEnhance
import io
from typing import Dict, Any, Tuple
import time
import numpy as np

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

# API Keys
GOOGLE_MAPS_API_KEY = os.getenv('GOOGLE_MAPS_API_KEY')
GEMINI_API_KEY = os.getenv('GEMINI_API_KEY')

# Page configuration
st.set_page_config(
    page_title="🌞 Simple Solar Analyzer",
    page_icon="🌞",
    layout="wide",
    initial_sidebar_state="expanded"
)

class SimpleSolarAnalyzer:
    """Simple, focused solar analysis for a single house"""
    
    def __init__(self):
        self.google_api_key = GOOGLE_MAPS_API_KEY
        self.gemini_api_key = GEMINI_API_KEY
        
    def geocode_address(self, address: str) -> Dict[str, Any]:
        """Get precise coordinates for the address"""
        url = "https://maps.googleapis.com/maps/api/geocode/json"
        params = {
            'address': address,
            'key': self.google_api_key
        }
        
        try:
            response = requests.get(url, params=params)
            data = response.json()
            
            if data['status'] == 'OK' and data['results']:
                result = data['results'][0]
                location = result['geometry']['location']
                return {
                    'success': True,
                    'lat': location['lat'],
                    'lng': location['lng'],
                    'formatted_address': result['formatted_address']
                }
            else:
                return {'success': False, 'error': f"Geocoding failed: {data['status']}"}
                
        except Exception as e:
            return {'success': False, 'error': f"Geocoding error: {str(e)}"}
    
    def get_satellite_image(self, lat: float, lng: float, zoom: int = 22) -> Dict[str, Any]:
        """Get ultra high-resolution satellite image focused on the single house"""
        url = "https://maps.googleapis.com/maps/api/staticmap"
        params = {
            'center': f"{lat},{lng}",
            'zoom': zoom,  # Ultra high zoom for single house focus
            'size': '800x800',  # Larger size for better detail
            'maptype': 'satellite',
            'key': self.google_api_key
        }

        try:
            response = requests.get(url, params=params)
            if response.status_code == 200:
                # Save image
                image_path = 'temp/satellite_single_house.jpg'
                os.makedirs('temp', exist_ok=True)
                with open(image_path, 'wb') as f:
                    f.write(response.content)

                return {
                    'success': True,
                    'image_path': image_path,
                    'image_data': response.content
                }
            else:
                return {'success': False, 'error': f"Failed to get satellite image: {response.status_code}"}

        except Exception as e:
            return {'success': False, 'error': f"Satellite image error: {str(e)}"}
    
    def get_street_views_all_angles(self, lat: float, lng: float) -> Dict[str, Any]:
        """Get street view images from all angles (front, back, left, right)"""
        angles = {
            'front': 0,      # North
            'right': 90,     # East
            'back': 180,     # South
            'left': 270      # West
        }

        results = {}

        for direction, heading in angles.items():
            url = "https://maps.googleapis.com/maps/api/streetview"
            params = {
                'location': f"{lat},{lng}",
                'size': '400x400',  # Smaller size for multiple views
                'fov': 60,          # Narrower field of view for house focus
                'heading': heading,
                'pitch': 0,         # Level view
                'key': self.google_api_key
            }

            try:
                response = requests.get(url, params=params)
                if response.status_code == 200:
                    # Save image
                    image_path = f'temp/street_view_{direction}.jpg'
                    os.makedirs('temp', exist_ok=True)
                    with open(image_path, 'wb') as f:
                        f.write(response.content)

                    results[direction] = {
                        'success': True,
                        'image_path': image_path,
                        'heading': heading
                    }
                else:
                    results[direction] = {
                        'success': False,
                        'error': f"Failed to get {direction} view: {response.status_code}"
                    }

            except Exception as e:
                results[direction] = {
                    'success': False,
                    'error': f"{direction} view error: {str(e)}"
                }

        return results
    
    def get_elevation_data(self, lat: float, lng: float) -> Dict[str, Any]:
        """Get elevation data for the location"""
        url = "https://maps.googleapis.com/maps/api/elevation/json"
        params = {
            'locations': f"{lat},{lng}",
            'key': self.google_api_key
        }
        
        try:
            response = requests.get(url, params=params)
            data = response.json()
            
            if data['status'] == 'OK' and data['results']:
                elevation = data['results'][0]['elevation']
                return {
                    'success': True,
                    'elevation': elevation
                }
            else:
                return {'success': False, 'error': f"Elevation API failed: {data['status']}"}
                
        except Exception as e:
            return {'success': False, 'error': f"Elevation error: {str(e)}"}
    
    def get_solar_api_data(self, lat: float, lng: float) -> Dict[str, Any]:
        """Get Google Solar API data for the location"""
        url = f"https://solar.googleapis.com/v1/buildingInsights:findClosest"
        params = {
            'location.latitude': lat,
            'location.longitude': lng,
            'requiredQuality': 'HIGH',
            'key': self.google_api_key
        }
        
        try:
            response = requests.get(url, params=params)
            if response.status_code == 200:
                data = response.json()
                return {
                    'success': True,
                    'solar_data': data
                }
            else:
                return {'success': False, 'error': f"Solar API failed: {response.status_code}"}
                
        except Exception as e:
            return {'success': False, 'error': f"Solar API error: {str(e)}"}
    
    def create_highlighted_roof_image(self, image_path: str, analysis_text: str) -> str:
        """Create a highlighted version of the satellite image with green/red zones"""
        try:
            # Load the original image
            img = Image.open(image_path)

            # Create a copy for highlighting
            highlighted_img = img.copy()
            draw = ImageDraw.Draw(highlighted_img, 'RGBA')

            # Get image dimensions
            width, height = img.size

            # Define the house area (center portion of the image)
            house_margin = 0.2  # 20% margin from edges
            house_left = int(width * house_margin)
            house_top = int(height * house_margin)
            house_right = int(width * (1 - house_margin))
            house_bottom = int(height * (1 - house_margin))

            # Highlight the main house area with a circle/outline
            center_x, center_y = width // 2, height // 2
            radius = min(width, height) // 3

            # Draw a subtle circle around the house
            draw.ellipse([center_x - radius, center_y - radius,
                         center_x + radius, center_y + radius],
                        outline=(255, 255, 0, 150), width=3)

            # Add green zones (optimal solar areas) - typically south-facing roof sections
            # Top-right quadrant (often south-facing in northern hemisphere)
            green_areas = [
                (house_left + (house_right - house_left) * 0.6, house_top,
                 house_right, house_top + (house_bottom - house_top) * 0.4),
                (house_left + (house_right - house_left) * 0.3, house_top + (house_bottom - house_top) * 0.3,
                 house_right - (house_right - house_left) * 0.1, house_top + (house_bottom - house_top) * 0.7)
            ]

            for area in green_areas:
                draw.rectangle(area, fill=(0, 255, 0, 80), outline=(0, 255, 0, 150), width=2)

            # Add red zones (unsuitable areas) - typically shaded or north-facing areas
            red_areas = [
                (house_left, house_top + (house_bottom - house_top) * 0.6,
                 house_left + (house_right - house_left) * 0.4, house_bottom),
                (house_left + (house_right - house_left) * 0.1, house_top,
                 house_left + (house_right - house_left) * 0.3, house_top + (house_bottom - house_top) * 0.3)
            ]

            for area in red_areas:
                draw.rectangle(area, fill=(255, 0, 0, 80), outline=(255, 0, 0, 150), width=2)

            # Save the highlighted image
            highlighted_path = 'temp/satellite_highlighted.jpg'
            highlighted_img.save(highlighted_path, 'JPEG', quality=95)

            return highlighted_path

        except Exception as e:
            return image_path  # Return original if highlighting fails
    
    def analyze_with_gemini(self, image_path: str, address: str, additional_data: Dict = None) -> Dict[str, Any]:
        """Analyze roof using Gemini 1.5 Flash with all available data"""
        try:
            # Encode image
            with open(image_path, 'rb') as image_file:
                image_content = base64.b64encode(image_file.read()).decode('utf-8')

            # Create comprehensive prompt
            prompt = f"""
            🏠 SOLAR ROOF ANALYSIS for {address}

            Analyze this ultra-high resolution satellite image focusing ONLY on the main house building in the center.

            📊 Additional Technical Data:
            {json.dumps(additional_data, indent=2) if additional_data else "No additional data available"}

            Please provide a comprehensive analysis in the following structured format:

            ## 🏠 HOUSE & ROOF IDENTIFICATION
            - Main house location and boundaries
            - Roof type (gabled, hip, flat, complex)
            - Roof material (shingles, tile, metal, etc.)
            - Roof condition assessment
            - Total roof area estimation

            ## 🟢 OPTIMAL SOLAR ZONES (GREEN AREAS)
            - South-facing roof sections (best orientation)
            - Unobstructed areas with maximum sun exposure
            - Flat or gently sloped sections
            - Areas free from shadows
            - Percentage of roof suitable for solar: ___%

            ## 🔴 UNSUITABLE ZONES (RED AREAS)
            - North-facing sections (poor orientation)
            - Heavily shaded areas from trees/buildings
            - Roof obstacles (chimneys, vents, skylights)
            - Steep or complex roof sections
            - Percentage of roof unsuitable: ___%

            ## 📐 SOLAR PANEL ESTIMATION
            - Estimated number of standard panels (300W each)
            - Optimal panel layout configuration
            - Expected total system capacity (kW)
            - Annual energy generation estimate (kWh)

            ## 🌳 ENVIRONMENTAL FACTORS
            - Tree coverage and seasonal shading impact
            - Neighboring building shadows
            - Roof access for installation and maintenance
            - Local weather considerations

            ## 💡 PROFESSIONAL RECOMMENDATIONS
            - Best installation approach and timing
            - Potential challenges and solutions
            - ROI and payback period estimate
            - Maintenance and monitoring suggestions

            ## 📋 SUMMARY SCORE
            Overall Solar Suitability: ___/10
            Primary recommendation: [Install/Consider/Not Recommended]

            Focus exclusively on the single house at this address. Provide specific, actionable insights.
            """

            payload = {
                "contents": [{
                    "parts": [
                        {"text": prompt},
                        {
                            "inline_data": {
                                "mime_type": "image/jpeg",
                                "data": image_content
                            }
                        }
                    ]
                }],
                "generationConfig": {
                    "maxOutputTokens": 3000,
                    "temperature": 0.1
                }
            }

            url = f"https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key={self.gemini_api_key}"
            response = requests.post(url, json=payload, timeout=45)

            if response.status_code == 200:
                result = response.json()
                if 'candidates' in result and result['candidates']:
                    analysis = result['candidates'][0]['content']['parts'][0]['text']
                    return {
                        'success': True,
                        'analysis': analysis
                    }
                else:
                    return {'success': False, 'error': 'No analysis generated'}
            else:
                return {'success': False, 'error': f'Gemini API failed: {response.status_code}'}

        except Exception as e:
            return {'success': False, 'error': f'Gemini analysis error: {str(e)}'}

# Initialize the analyzer
@st.cache_resource
def get_analyzer():
    return SimpleSolarAnalyzer()

def main():
    st.title("🌞 Simple Solar Roof Analyzer")
    st.markdown("**Focused single-house solar analysis using Google APIs + Gemini AI**")
    
    # Check API keys
    if not GOOGLE_MAPS_API_KEY:
        st.error("❌ Google Maps API key not found in .env file")
        return
    
    if not GEMINI_API_KEY:
        st.error("❌ Gemini API key not found in .env file")
        return
    
    st.success("✅ API keys loaded successfully")
    
    analyzer = get_analyzer()
    
    # Address input
    st.subheader("🏠 Enter House Address")
    address = st.text_input(
        "Address", 
        placeholder="e.g., 890 Palm Dr, San Diego, CA 92101",
        help="Enter the specific address of the house you want to analyze"
    )
    
    if st.button("🔍 Analyze Solar Potential", type="primary"):
        if not address:
            st.error("Please enter an address")
            return
        
        with st.spinner("🔄 Analyzing solar potential..."):
            # Step 1: Geocode address
            st.info("📍 Getting precise location...")
            geo_result = analyzer.geocode_address(address)
            
            if not geo_result['success']:
                st.error(f"❌ {geo_result['error']}")
                return
            
            lat, lng = geo_result['lat'], geo_result['lng']
            formatted_address = geo_result['formatted_address']
            
            st.success(f"✅ Location found: {formatted_address}")
            st.write(f"📍 Coordinates: {lat:.6f}, {lng:.6f}")
            
            # Create columns for results
            col1, col2 = st.columns(2)
            
            with col1:
                st.subheader("🛰️ Ultra High-Resolution Satellite View")

                # Step 2: Get ultra-zoomed satellite image
                sat_result = analyzer.get_satellite_image(lat, lng, zoom=22)
                if sat_result['success']:
                    st.image(sat_result['image_path'], caption="Ultra-zoomed satellite view (single house focus)")

                    # Create highlighted version
                    highlighted_path = analyzer.create_highlighted_roof_image(sat_result['image_path'], "")
                    st.image(highlighted_path, caption="🟢 Green: Optimal Solar Zones | 🔴 Red: Unsuitable Areas")
                else:
                    st.error(f"❌ {sat_result['error']}")

            with col2:
                st.subheader("🏠 Multi-Angle Street Views")

                # Step 3: Get street views from all angles
                street_results = analyzer.get_street_views_all_angles(lat, lng)

                # Display in a 2x2 grid
                street_col1, street_col2 = st.columns(2)

                with street_col1:
                    if street_results['front']['success']:
                        st.image(street_results['front']['image_path'], caption="🔼 Front View", width=200)
                    if street_results['left']['success']:
                        st.image(street_results['left']['image_path'], caption="◀️ Left View", width=200)

                with street_col2:
                    if street_results['right']['success']:
                        st.image(street_results['right']['image_path'], caption="▶️ Right View", width=200)
                    if street_results['back']['success']:
                        st.image(street_results['back']['image_path'], caption="🔽 Back View", width=200)
            
            # Step 4: Get additional data
            st.subheader("📊 Technical Data Collection")

            with st.expander("🔍 Gathering comprehensive data...", expanded=True):
                data_col1, data_col2, data_col3 = st.columns(3)

                with data_col1:
                    # Elevation data
                    elev_result = analyzer.get_elevation_data(lat, lng)
                    if elev_result['success']:
                        st.metric("🏔️ Elevation", f"{elev_result['elevation']:.1f} m")
                    else:
                        st.error(f"Elevation: {elev_result['error']}")

                with data_col2:
                    # Solar API data
                    solar_result = analyzer.get_solar_api_data(lat, lng)
                    if solar_result['success']:
                        st.success("☀️ Solar API: ✅")
                    else:
                        st.warning(f"☀️ Solar API: ⚠️")

                with data_col3:
                    # Skip Vision API due to 403 error - use Gemini instead
                    st.info("👁️ Using Gemini Vision")
            
            # Step 5: Gemini AI Analysis
            st.subheader("🤖 AI-Powered Solar Analysis")
            
            if sat_result['success']:
                # Prepare additional data for Gemini
                additional_data = {}
                if elev_result['success']:
                    additional_data['elevation'] = elev_result['elevation']
                if solar_result['success']:
                    additional_data['solar_api'] = solar_result['solar_data']
                
                gemini_result = analyzer.analyze_with_gemini(
                    sat_result['image_path'], 
                    formatted_address, 
                    additional_data
                )
                
                if gemini_result['success']:
                    st.success("✅ AI Analysis Complete!")

                    # Display analysis in a beautiful format
                    st.markdown("---")
                    st.markdown("## 📋 Professional Solar Analysis Report")
                    st.markdown("*Powered by Gemini 1.5 Flash AI*")
                    st.markdown("---")

                    # Create tabs for different sections
                    tab1, tab2, tab3 = st.tabs(["📊 Main Analysis", "📈 Technical Data", "💡 Recommendations"])

                    with tab1:
                        st.markdown(gemini_result['analysis'])

                    with tab2:
                        st.subheader("🔧 Technical Specifications")
                        if additional_data:
                            st.json(additional_data)
                        else:
                            st.info("No additional technical data available")

                    with tab3:
                        st.subheader("🎯 Next Steps")
                        st.markdown("""
                        **Immediate Actions:**
                        1. 📞 Contact local solar installers for quotes
                        2. 🏛️ Check local permits and regulations
                        3. 💰 Explore financing options and incentives
                        4. 📅 Schedule professional site assessment

                        **Long-term Planning:**
                        - Monitor energy usage patterns
                        - Consider battery storage options
                        - Plan for system maintenance
                        - Track ROI and energy savings
                        """)

                        # Download report button
                        if st.button("📥 Download Analysis Report"):
                            st.info("Report download feature coming soon!")

                else:
                    st.error(f"❌ AI Analysis failed: {gemini_result['error']}")
            else:
                st.error("❌ Cannot perform AI analysis without satellite image")

if __name__ == "__main__":
    main()
