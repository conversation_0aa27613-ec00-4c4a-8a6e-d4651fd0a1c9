#!/usr/bin/env python3
"""
Debug NASA Earth Imagery API Integration
Comprehensive testing of satellite image acquisition system
"""

import os
import sys
import requests
import time
import json
from datetime import datetime, timedelta

# Add the current directory to Python path
script_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(script_dir)
os.chdir(script_dir)

def test_nasa_api_direct():
    """Test NASA Earth Imagery API directly with various parameters"""
    print("🛰️ Testing NASA Earth Imagery API Directly")
    print("-" * 60)
    
    # Test coordinates (various locations)
    test_locations = [
        {"name": "New York City", "lat": 40.7128, "lng": -74.0060},
        {"name": "Los Angeles", "lat": 34.0522, "lng": -118.2437},
        {"name": "London, UK", "lat": 51.5074, "lng": -0.1278},
        {"name": "Paris, France", "lat": 48.8566, "lng": 2.3522},
        {"name": "Sydney, Australia", "lat": -33.8688, "lng": 151.2093}
    ]
    
    # Test different date ranges
    date_ranges = [
        (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d'),
        (datetime.now() - timedelta(days=60)).strftime('%Y-%m-%d'),
        (datetime.now() - timedelta(days=90)).strftime('%Y-%m-%d'),
        '2023-06-01',
        '2023-01-01',
        '2022-06-01'
    ]
    
    # Test different dimensions
    dimensions = [0.1, 0.15, 0.2, 0.25]
    
    url = "https://api.nasa.gov/planetary/earth/imagery"
    
    results = {
        'successful_requests': 0,
        'failed_requests': 0,
        'error_types': {},
        'successful_combinations': []
    }
    
    for location in test_locations[:2]:  # Test first 2 locations to avoid rate limiting
        print(f"\n📍 Testing location: {location['name']} ({location['lat']}, {location['lng']})")
        
        for date in date_ranges[:3]:  # Test first 3 dates
            for dim in dimensions[:2]:  # Test first 2 dimensions
                print(f"   📅 Date: {date}, 📏 Dimension: {dim}")
                
                params = {
                    'lat': location['lat'],
                    'lon': location['lng'],
                    'date': date,
                    'dim': dim,
                    'api_key': 'DEMO_KEY'
                }
                
                try:
                    response = requests.get(url, params=params, timeout=30)
                    
                    print(f"      Status: {response.status_code}")
                    print(f"      Content-Type: {response.headers.get('content-type', 'Unknown')}")
                    print(f"      Content-Length: {len(response.content)} bytes")
                    
                    if response.status_code == 200:
                        content_type = response.headers.get('content-type', '')
                        if 'image' in content_type and len(response.content) > 1000:
                            print(f"      ✅ SUCCESS: Valid image received")
                            results['successful_requests'] += 1
                            results['successful_combinations'].append({
                                'location': location['name'],
                                'lat': location['lat'],
                                'lng': location['lng'],
                                'date': date,
                                'dim': dim,
                                'size': len(response.content)
                            })
                        else:
                            print(f"      ❌ INVALID: Non-image content or too small")
                            results['failed_requests'] += 1
                    else:
                        error_type = f"HTTP_{response.status_code}"
                        results['error_types'][error_type] = results['error_types'].get(error_type, 0) + 1
                        results['failed_requests'] += 1
                        print(f"      ❌ ERROR: {response.status_code}")
                        if response.text:
                            print(f"      Response: {response.text[:100]}...")
                
                except Exception as e:
                    error_type = type(e).__name__
                    results['error_types'][error_type] = results['error_types'].get(error_type, 0) + 1
                    results['failed_requests'] += 1
                    print(f"      💥 EXCEPTION: {str(e)}")
                
                # Be respectful to the API
                time.sleep(1)
    
    return results

def test_api_key_variations():
    """Test different API key configurations"""
    print("\n\n🔑 Testing API Key Variations")
    print("-" * 60)
    
    api_keys = [
        'DEMO_KEY',
        '',  # Empty key
        'INVALID_KEY',
        None
    ]
    
    url = "https://api.nasa.gov/planetary/earth/imagery"
    test_params = {
        'lat': 40.7128,
        'lon': -74.0060,
        'date': '2023-06-01',
        'dim': 0.15
    }
    
    for api_key in api_keys:
        print(f"\n🔑 Testing API key: {api_key}")
        
        params = test_params.copy()
        if api_key is not None:
            params['api_key'] = api_key
        
        try:
            response = requests.get(url, params=params, timeout=10)
            print(f"   Status: {response.status_code}")
            print(f"   Content-Type: {response.headers.get('content-type', 'Unknown')}")
            
            if response.status_code != 200:
                print(f"   Error Response: {response.text[:200]}")
        
        except Exception as e:
            print(f"   Exception: {str(e)}")
        
        time.sleep(1)

def test_fallback_system():
    """Test the fallback system to local test images"""
    print("\n\n📁 Testing Fallback System")
    print("-" * 60)
    
    test_images = [
        'temp/satellite_single_house.jpg',
        'temp/satellite_professional_highlighted.jpg',
        'temp/test_satellite.jpg'
    ]
    
    print("Checking test image availability:")
    available_images = []
    
    for img_path in test_images:
        if os.path.exists(img_path):
            size = os.path.getsize(img_path)
            print(f"   ✅ {img_path} - {size:,} bytes")
            available_images.append(img_path)
        else:
            print(f"   ❌ {img_path} - NOT FOUND")
    
    print(f"\nAvailable fallback images: {len(available_images)}/{len(test_images)}")
    
    if available_images:
        print("✅ Fallback system should work")
        return True
    else:
        print("❌ Fallback system will fail - no test images available")
        return False

def test_app_integration():
    """Test satellite image retrieval through the application"""
    print("\n\n🏠 Testing Application Integration")
    print("-" * 60)
    
    try:
        from app import EnhancedSolarAnalyzer
        analyzer = EnhancedSolarAnalyzer()
        
        test_coordinates = [
            {"name": "New York", "lat": 40.7128, "lng": -74.0060},
            {"name": "Los Angeles", "lat": 34.0522, "lng": -118.2437}
        ]
        
        for coord in test_coordinates:
            print(f"\n📍 Testing {coord['name']}: {coord['lat']}, {coord['lng']}")
            
            result = analyzer.get_satellite_image(coord['lat'], coord['lng'])
            
            print(f"   Success: {result['success']}")
            if result['success']:
                print(f"   Image Path: {result['image_path']}")
                print(f"   Source: {result.get('source', 'Unknown')}")
                if 'warning' in result:
                    print(f"   Warning: {result['warning']}")
            else:
                print(f"   Error: {result['error']}")
                if 'suggestion' in result:
                    print(f"   Suggestion: {result['suggestion']}")
            
            time.sleep(2)  # Delay between tests
        
        return True
        
    except Exception as e:
        print(f"❌ Application integration test failed: {str(e)}")
        return False

def main():
    """Run comprehensive NASA satellite imagery diagnostics"""
    print("🧪 NASA Satellite Imagery Diagnostic Suite")
    print("=" * 70)
    
    # Test 1: Direct NASA API testing
    api_results = test_nasa_api_direct()
    
    # Test 2: API key variations
    test_api_key_variations()
    
    # Test 3: Fallback system
    fallback_available = test_fallback_system()
    
    # Test 4: Application integration
    app_working = test_app_integration()
    
    # Summary
    print("\n\n📊 Diagnostic Summary")
    print("=" * 40)
    
    print(f"NASA API Direct Tests:")
    print(f"   Successful: {api_results['successful_requests']}")
    print(f"   Failed: {api_results['failed_requests']}")
    print(f"   Error Types: {api_results['error_types']}")
    
    if api_results['successful_combinations']:
        print(f"\n✅ Working combinations found:")
        for combo in api_results['successful_combinations']:
            print(f"   • {combo['location']}: {combo['date']}, dim={combo['dim']}")
    
    print(f"\nFallback System: {'✅ Available' if fallback_available else '❌ Not Available'}")
    print(f"App Integration: {'✅ Working' if app_working else '❌ Failed'}")
    
    # Recommendations
    print(f"\n💡 Recommendations:")
    if api_results['successful_requests'] > 0:
        print("   • NASA API is working for some combinations")
        print("   • Consider optimizing date/dimension parameters")
    else:
        print("   • NASA API appears to be having issues")
        print("   • DEMO_KEY may have limitations")
        print("   • Consider getting a real NASA API key")
    
    if not fallback_available:
        print("   • Add test images to temp/ directory for fallback")
    
    if not app_working:
        print("   • Check application configuration and dependencies")

if __name__ == "__main__":
    main()
