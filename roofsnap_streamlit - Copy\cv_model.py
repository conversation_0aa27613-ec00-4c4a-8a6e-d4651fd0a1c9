# backend/app/services/vision.py

import cv2
import numpy as np
import os
from typing import List, Dict, Tuple
from PIL import Image, ImageDraw
import matplotlib.pyplot as plt
from scipy import ndimage

# Constants
PANEL_WIDTH_M = 1.6
PANEL_HEIGHT_M = 1.0

def detect_roof_segmentation(image_path: str) -> np.ndarray:
    """
    Advanced roof segmentation using multiple computer vision techniques.
    Combines edge detection, color analysis, and morphological operations.
    """
    image = cv2.imread(image_path)
    if image is None:
        raise ValueError(f"Could not load image from {image_path}")

    # Convert to different color spaces for analysis
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
    lab = cv2.cvtColor(image, cv2.COLOR_BGR2LAB)

    # Method 1: Adaptive thresholding for roof materials
    adaptive_thresh = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                                          cv2.THRESH_BINARY, 11, 2)

    # Method 2: Edge detection with Canny
    edges = cv2.Canny(gray, 50, 150, apertureSize=3)

    # Method 3: Color-based segmentation for typical roof colors
    # Define roof color ranges (gray, brown, red, blue roofs)
    roof_mask = np.zeros(gray.shape, dtype=np.uint8)

    # Gray roofs (most common)
    gray_lower = np.array([0, 0, 50])
    gray_upper = np.array([180, 30, 200])
    gray_roof = cv2.inRange(hsv, gray_lower, gray_upper)

    # Brown/tan roofs
    brown_lower = np.array([10, 50, 50])
    brown_upper = np.array([25, 255, 200])
    brown_roof = cv2.inRange(hsv, brown_lower, brown_upper)

    # Red/clay roofs
    red_lower = np.array([0, 50, 50])
    red_upper = np.array([10, 255, 255])
    red_roof = cv2.inRange(hsv, red_lower, red_upper)

    # Combine color masks
    roof_mask = cv2.bitwise_or(roof_mask, gray_roof)
    roof_mask = cv2.bitwise_or(roof_mask, brown_roof)
    roof_mask = cv2.bitwise_or(roof_mask, red_roof)

    # Method 4: Texture analysis using local binary patterns
    texture_mask = analyze_roof_texture(gray)

    # Combine all methods
    combined_mask = cv2.bitwise_or(roof_mask, texture_mask)
    combined_mask = cv2.bitwise_or(combined_mask, adaptive_thresh)

    # Morphological operations to clean up the mask
    kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
    combined_mask = cv2.morphologyEx(combined_mask, cv2.MORPH_CLOSE, kernel)
    combined_mask = cv2.morphologyEx(combined_mask, cv2.MORPH_OPEN, kernel)

    # Fill holes in the mask
    combined_mask = ndimage.binary_fill_holes(combined_mask).astype(np.uint8) * 255

    return combined_mask

def analyze_roof_texture(gray_image: np.ndarray) -> np.ndarray:
    """
    Analyze texture patterns typical of roof materials (shingles, tiles, etc.)
    """
    # Apply Gabor filters to detect roof texture patterns
    texture_responses = []

    # Different orientations and frequencies for roof patterns
    orientations = [0, 45, 90, 135]  # degrees
    frequencies = [0.1, 0.3, 0.5]

    for angle in orientations:
        for freq in frequencies:
            kernel = cv2.getGaborKernel((21, 21), 5, np.radians(angle),
                                      2*np.pi*freq, 0.5, 0, ktype=cv2.CV_32F)
            filtered = cv2.filter2D(gray_image, cv2.CV_8UC3, kernel)
            texture_responses.append(filtered)

    # Combine texture responses
    texture_combined = np.zeros_like(gray_image, dtype=np.float32)
    for response in texture_responses:
        texture_combined += np.abs(response.astype(np.float32))

    # Normalize and threshold
    texture_combined = (texture_combined / len(texture_responses)).astype(np.uint8)
    _, texture_mask = cv2.threshold(texture_combined, 30, 255, cv2.THRESH_BINARY)

    return texture_mask

def detect_roof_outline(image_path: str) -> Tuple[np.ndarray, List[np.ndarray]]:
    """
    Detect precise roof outline/contours like in the example image.
    Returns the processed image and list of roof contours.
    """
    image = cv2.imread(image_path)
    if image is None:
        raise ValueError(f"Could not load image from {image_path}")

    original = image.copy()

    # Get roof segmentation mask
    roof_mask = detect_roof_segmentation(image_path)

    # Find contours of the roof
    contours, _ = cv2.findContours(roof_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

    # Filter contours by area to get main roof structures
    min_area = (image.shape[0] * image.shape[1]) * 0.01  # At least 1% of image
    roof_contours = [cnt for cnt in contours if cv2.contourArea(cnt) > min_area]

    # Sort by area (largest first)
    roof_contours = sorted(roof_contours, key=cv2.contourArea, reverse=True)

    # Take the largest contours (main roof structures)
    main_roof_contours = roof_contours[:3]  # Top 3 largest

    return original, main_roof_contours

def draw_roof_outline(image: np.ndarray, contours: List[np.ndarray],
                     outline_color=(0, 255, 255), thickness=4) -> np.ndarray:
    """
    Draw yellow roof outline on the image like in the example.
    """
    result = image.copy()

    # Draw each roof contour
    for contour in contours:
        # Smooth the contour
        epsilon = 0.02 * cv2.arcLength(contour, True)
        smoothed = cv2.approxPolyDP(contour, epsilon, True)

        # Draw the outline
        cv2.drawContours(result, [smoothed], -1, outline_color, thickness)

    return result

def create_roof_analysis_overlay(image_path: str) -> Dict[str, str]:
    """
    Create comprehensive roof analysis with outline detection and panel zones.
    Returns paths to different analysis images.
    """
    try:
        # Load original image
        original_image = cv2.imread(image_path)
        if original_image is None:
            raise ValueError(f"Could not load image from {image_path}")

        # 1. Detect roof outline
        _, roof_contours = detect_roof_outline(image_path)

        # 2. Create outline-only image (like left side of example)
        outline_image = draw_roof_outline(original_image, roof_contours)
        outline_path = image_path.replace('.jpg', '_roof_outline.jpg')
        cv2.imwrite(outline_path, outline_image)

        # 3. Create panel detection overlay (like right side of example)
        panel_result = generate_roof_panel_layout(image_path)
        panel_overlay_path = panel_result.get('overlay_image_path', image_path)

        # 4. Create combined analysis image
        combined_image = create_combined_analysis(outline_image, panel_overlay_path)
        combined_path = image_path.replace('.jpg', '_combined_analysis.jpg')
        cv2.imwrite(combined_path, combined_image)

        return {
            'original': image_path,
            'roof_outline': outline_path,
            'panel_overlay': panel_overlay_path,
            'combined_analysis': combined_path,
            'summary': panel_result.get('summary', {}),
            'panel_positions': panel_result.get('panel_positions', []),
            'heatmap': panel_result.get('heatmap', [])
        }

    except Exception as e:
        print(f"Error in roof analysis: {str(e)}")
        return {
            'original': image_path,
            'error': str(e)
        }

def create_combined_analysis(outline_image: np.ndarray, panel_overlay_path: str) -> np.ndarray:
    """
    Create side-by-side comparison like in the example image.
    """
    try:
        # Load panel overlay image
        panel_image = cv2.imread(panel_overlay_path)
        if panel_image is None:
            panel_image = outline_image.copy()

        # Resize images to same height
        height = min(outline_image.shape[0], panel_image.shape[0])
        outline_resized = cv2.resize(outline_image,
                                   (int(outline_image.shape[1] * height / outline_image.shape[0]), height))
        panel_resized = cv2.resize(panel_image,
                                 (int(panel_image.shape[1] * height / panel_image.shape[0]), height))

        # Create side-by-side image
        combined = np.hstack([outline_resized, panel_resized])

        # Add labels
        font = cv2.FONT_HERSHEY_SIMPLEX
        cv2.putText(combined, "High-resolution satellite view", (10, 30),
                   font, 0.8, (255, 255, 255), 2)
        cv2.putText(combined, "Computer Vision panel detection",
                   (outline_resized.shape[1] + 10, 30), font, 0.8, (255, 255, 255), 2)

        return combined

    except Exception as e:
        print(f"Error creating combined analysis: {str(e)}")
        return outline_image

def estimate_roof_pitch_and_azimuth(roof_mask: np.ndarray) -> Dict:
    """
    Dummy estimator for pitch and azimuth using image dimensions.
    Replace with real elevation data via API or angle estimation later.
    """
    return {
        "pitch": 20.0,
        "azimuth": 180.0,
        "flat_projection": roof_mask.copy()
    }

def tile_roof_with_panels(mask: np.ndarray, elevation_info: Dict) -> List[Tuple[int, int, int, int]]:
    """
    Tiles the roof area with solar panel-sized rectangles.
    Returns list of rectangles (x, y, w, h).
    """
    h, w = mask.shape
    grid = []
    step_x, step_y = 40, 25  # pixels per panel (example, calibrate per zoom)

    for y in range(0, h - step_y, step_y):
        for x in range(0, w - step_x, step_x):
            region = mask[y:y+step_y, x:x+step_x]
            if np.mean(region) > 100:  # mostly within roof
                grid.append((x, y, step_x, step_y))

    return grid

def estimate_solar_heatmap(panel_grid: List[Tuple[int, int, int, int]], elevation_info: Dict) -> List[int]:
    """
    Generate dummy heatmap scores per panel.
    """
    return [np.random.randint(60, 100) for _ in panel_grid]

def draw_panel_overlay(image_path: str, panel_grid: List[Tuple[int, int, int, int]], heatmap: List[int]) -> np.ndarray:
    """
    Draws colored panel rectangles on the image.
    Returns annotated image (np.ndarray).
    """
    image = cv2.imread(image_path)
    overlay = image.copy()

    for (x, y, w, h), score in zip(panel_grid, heatmap):
        color = (0, int(255 * (score / 100)), 0)
        cv2.rectangle(overlay, (x, y), (x + w, y + h), color, -1)

    blended = cv2.addWeighted(overlay, 0.4, image, 0.6, 0)
    return blended

def generate_summary(panel_grid: List[Tuple[int, int, int, int]], heatmap: List[int], elevation_info: Dict) -> Dict:
    panel_area = PANEL_WIDTH_M * PANEL_HEIGHT_M
    total_panels = len(panel_grid)
    usable_area = total_panels * panel_area
    avg_score = np.mean(heatmap)
    kwh_est = usable_area * avg_score * 0.015  # Dummy multiplier

    return {
        "panel_count": total_panels,
        "usable_area_m2": round(usable_area, 2),
        "estimated_kwh_per_year": round(kwh_est, 2),
        "average_efficiency_score": round(avg_score, 1)
    }

def generate_roof_panel_layout(image_path: str) -> Dict:
    """
    Enhanced roof panel layout generation with improved detection.
    """
    try:
        roof_mask = detect_roof_segmentation(image_path)
        elevation_info = estimate_roof_pitch_and_azimuth(roof_mask)
        panel_grid = tile_roof_with_panels(roof_mask, elevation_info)
        heatmap = estimate_solar_heatmap(panel_grid, elevation_info)
        overlaid_image = draw_panel_overlay(image_path, panel_grid, heatmap)
        summary = generate_summary(panel_grid, heatmap, elevation_info)

        output_path = image_path.replace('.jpg', '_overlay.jpg')
        cv2.imwrite(output_path, overlaid_image)

        return {
            "overlay_image_path": output_path,
            "summary": summary,
            "panel_positions": panel_grid,
            "heatmap": heatmap
        }
    except Exception as e:
        print(f"Error in panel layout generation: {str(e)}")
        return {
            "overlay_image_path": image_path,
            "summary": {"error": str(e)},
            "panel_positions": [],
            "heatmap": []
        }

# Main function for comprehensive roof analysis
def analyze_roof_comprehensive(image_path: str) -> Dict:
    """
    Main function that provides comprehensive roof analysis including:
    1. Roof outline detection (yellow boundary like in example)
    2. Solar panel placement analysis
    3. Combined visualization
    """
    print(f"Starting comprehensive roof analysis for: {image_path}")

    try:
        # Create comprehensive analysis
        analysis_result = create_roof_analysis_overlay(image_path)

        # Add roof outline detection specifically
        if 'error' not in analysis_result:
            original_image, roof_contours = detect_roof_outline(image_path)

            # Create the yellow outline image like in the example
            yellow_outline_image = draw_roof_outline(original_image, roof_contours,
                                                   outline_color=(0, 255, 255), thickness=6)

            # Save yellow outline version
            yellow_outline_path = image_path.replace('.jpg', '_yellow_outline.jpg')
            cv2.imwrite(yellow_outline_path, yellow_outline_image)

            analysis_result['yellow_outline'] = yellow_outline_path
            analysis_result['roof_contours_count'] = len(roof_contours)

            # Calculate roof area from contours
            total_roof_area = sum(cv2.contourArea(cnt) for cnt in roof_contours)
            analysis_result['roof_area_pixels'] = total_roof_area

        print(f"Analysis complete. Results: {list(analysis_result.keys())}")
        return analysis_result

    except Exception as e:
        print(f"Error in comprehensive analysis: {str(e)}")
        return {
            'original': image_path,
            'error': str(e)
        }

# Integration function for Streamlit app
def create_streamlit_roof_analysis(image_path: str) -> Dict:
    """
    Streamlit-specific function that creates the roof outline like in your example.
    Returns paths to the generated images for display in Streamlit.
    """
    print(f"🏠 Creating Streamlit roof analysis for: {image_path}")

    try:
        # Ensure temp directory exists
        os.makedirs('temp', exist_ok=True)

        # Run comprehensive analysis
        analysis_result = analyze_roof_comprehensive(image_path)

        if 'error' in analysis_result:
            return analysis_result

        # Create the specific images needed for Streamlit display
        original_image = cv2.imread(image_path)
        _, roof_contours = detect_roof_outline(image_path)

        # 1. Yellow outline image (like left side of your example)
        yellow_outline = draw_roof_outline(original_image, roof_contours,
                                         outline_color=(0, 255, 255), thickness=6)
        yellow_path = 'temp/roof_yellow_outline.jpg'
        cv2.imwrite(yellow_path, yellow_outline)

        # 2. Panel detection overlay (like right side of your example)
        panel_result = generate_roof_panel_layout(image_path)
        panel_path = panel_result.get('overlay_image_path', image_path)

        # 3. Side-by-side comparison
        comparison_image = create_side_by_side_comparison(yellow_outline, panel_path)
        comparison_path = 'temp/roof_comparison.jpg'
        cv2.imwrite(comparison_path, comparison_image)

        # 4. Calculate roof statistics
        total_roof_area = sum(cv2.contourArea(cnt) for cnt in roof_contours)
        roof_stats = {
            'contours_found': len(roof_contours),
            'total_roof_area_pixels': int(total_roof_area),
            'estimated_roof_area_sqft': int(total_roof_area * 0.1),  # Rough conversion
            'panel_count': len(panel_result.get('panel_positions', [])),
            'suitability_score': min(100, int(total_roof_area / 1000))  # Simple score
        }

        return {
            'success': True,
            'original_image': image_path,
            'yellow_outline': yellow_path,
            'panel_overlay': panel_path,
            'comparison_image': comparison_path,
            'roof_stats': roof_stats,
            'summary': panel_result.get('summary', {}),
            'panel_positions': panel_result.get('panel_positions', []),
            'heatmap': panel_result.get('heatmap', [])
        }

    except Exception as e:
        print(f"❌ Streamlit analysis error: {str(e)}")
        return {
            'success': False,
            'error': str(e),
            'original_image': image_path
        }

def create_side_by_side_comparison(yellow_outline_image: np.ndarray, panel_overlay_path: str) -> np.ndarray:
    """
    Create side-by-side comparison image for Streamlit display
    """
    try:
        # Load panel overlay
        if os.path.exists(panel_overlay_path):
            panel_image = cv2.imread(panel_overlay_path)
        else:
            panel_image = yellow_outline_image.copy()

        # Resize to same height
        height = 400
        yellow_resized = cv2.resize(yellow_outline_image,
                                  (int(yellow_outline_image.shape[1] * height / yellow_outline_image.shape[0]), height))
        panel_resized = cv2.resize(panel_image,
                                 (int(panel_image.shape[1] * height / panel_image.shape[0]), height))

        # Create side-by-side
        comparison = np.hstack([yellow_resized, panel_resized])

        # Add labels
        font = cv2.FONT_HERSHEY_SIMPLEX
        cv2.putText(comparison, "High-resolution satellite view", (10, 30),
                   font, 0.7, (255, 255, 255), 2)
        cv2.putText(comparison, "Computer Vision panel detection",
                   (yellow_resized.shape[1] + 10, 30), font, 0.7, (255, 255, 255), 2)

        return comparison

    except Exception as e:
        print(f"Error creating comparison: {str(e)}")
        return yellow_outline_image
