#!/usr/bin/env python3
"""
Debug Geocoding Issues
Test the OpenStreetMap Nominatim API directly and through our application
"""

import requests
import json
import time
import os
import sys

# Add the current directory to Python path
script_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(script_dir)
os.chdir(script_dir)

def test_nominatim_direct():
    """Test Nominatim API directly"""
    print("🌍 Testing OpenStreetMap Nominatim API directly...")
    
    test_addresses = [
        "1600 Amphitheatre Parkway, Mountain View, CA",
        "Times Square, New York, NY",
        "Eiffel Tower, Paris, France",
        "123 Main Street, Anytown, USA",
        "London, UK",
        "Tokyo, Japan"
    ]
    
    url = "https://nominatim.openstreetmap.org/search"
    headers = {
        'User-Agent': 'Solar-Roof-Analyzer/1.0 (NASA-API-Integration-Debug)'
    }
    
    results = {}
    
    for address in test_addresses:
        print(f"\n📍 Testing: {address}")
        
        params = {
            'q': address,
            'format': 'json',
            'limit': 1,
            'addressdetails': 1
        }
        
        try:
            response = requests.get(url, params=params, headers=headers, timeout=10)
            print(f"   Status: {response.status_code}")
            print(f"   Headers: {dict(response.headers)}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"   Response length: {len(data)}")
                
                if data and len(data) > 0:
                    location = data[0]
                    lat, lon = location['lat'], location['lon']
                    display_name = location['display_name']
                    print(f"   ✅ SUCCESS: {lat}, {lon}")
                    print(f"   📍 Address: {display_name}")
                    results[address] = {'success': True, 'lat': lat, 'lon': lon, 'display_name': display_name}
                else:
                    print(f"   ❌ EMPTY RESPONSE")
                    results[address] = {'success': False, 'error': 'Empty response'}
            else:
                print(f"   ❌ HTTP ERROR: {response.status_code}")
                print(f"   Response: {response.text[:200]}")
                results[address] = {'success': False, 'error': f'HTTP {response.status_code}'}
                
        except Exception as e:
            print(f"   ❌ EXCEPTION: {str(e)}")
            results[address] = {'success': False, 'error': str(e)}
        
        # Be respectful to the API
        time.sleep(1)
    
    return results

def test_app_geocoding():
    """Test geocoding through our application"""
    print("\n🏠 Testing geocoding through EnhancedSolarAnalyzer...")
    
    try:
        from app import EnhancedSolarAnalyzer
        analyzer = EnhancedSolarAnalyzer()
        
        test_addresses = [
            "1600 Amphitheatre Parkway, Mountain View, CA",
            "Times Square, New York, NY",
            "London, UK"
        ]
        
        results = {}
        
        for address in test_addresses:
            print(f"\n📍 Testing through app: {address}")
            result = analyzer.geocode_address(address)
            print(f"   Result: {result}")
            results[address] = result
            time.sleep(1)
        
        return results
        
    except Exception as e:
        print(f"❌ App geocoding test failed: {str(e)}")
        return {}

def test_rate_limiting():
    """Test if we're hitting rate limits"""
    print("\n⏱️ Testing rate limiting...")
    
    url = "https://nominatim.openstreetmap.org/search"
    headers = {
        'User-Agent': 'Solar-Roof-Analyzer/1.0 (Rate-Limit-Test)'
    }
    
    # Make several rapid requests
    for i in range(5):
        params = {
            'q': 'London, UK',
            'format': 'json',
            'limit': 1
        }
        
        try:
            start_time = time.time()
            response = requests.get(url, params=params, headers=headers, timeout=10)
            end_time = time.time()
            
            print(f"   Request {i+1}: Status {response.status_code}, Time: {end_time-start_time:.2f}s")
            
            if response.status_code == 429:
                print(f"   ⚠️ RATE LIMITED!")
                break
            elif response.status_code != 200:
                print(f"   ❌ Error: {response.text[:100]}")
                
        except Exception as e:
            print(f"   ❌ Exception: {str(e)}")
        
        time.sleep(0.5)  # Small delay between requests

def main():
    """Run all geocoding tests"""
    print("🧪 Geocoding Debug Test Suite")
    print("=" * 50)
    
    # Test 1: Direct Nominatim API
    direct_results = test_nominatim_direct()
    
    # Test 2: Through our application
    app_results = test_app_geocoding()
    
    # Test 3: Rate limiting
    test_rate_limiting()
    
    # Summary
    print("\n📊 Test Results Summary")
    print("=" * 30)
    
    print("\n🌍 Direct Nominatim API Results:")
    for address, result in direct_results.items():
        status = "✅ PASS" if result['success'] else "❌ FAIL"
        print(f"   {status}: {address}")
        if not result['success']:
            print(f"      Error: {result['error']}")
    
    print("\n🏠 App Geocoding Results:")
    for address, result in app_results.items():
        status = "✅ PASS" if result['success'] else "❌ FAIL"
        print(f"   {status}: {address}")
        if not result['success']:
            print(f"      Error: {result['error']}")
    
    # Analysis
    direct_success = sum(1 for r in direct_results.values() if r['success'])
    app_success = sum(1 for r in app_results.values() if r['success'])
    
    print(f"\n📈 Success Rates:")
    print(f"   Direct API: {direct_success}/{len(direct_results)} ({direct_success/len(direct_results)*100:.1f}%)")
    if app_results:
        print(f"   Through App: {app_success}/{len(app_results)} ({app_success/len(app_results)*100:.1f}%)")
    
    if direct_success > 0 and app_success == 0:
        print("\n🔍 DIAGNOSIS: API works directly but fails through app - check app implementation")
    elif direct_success == 0:
        print("\n🔍 DIAGNOSIS: API itself is having issues - network/rate limiting problem")
    else:
        print("\n🔍 DIAGNOSIS: Geocoding appears to be working")

if __name__ == "__main__":
    main()
