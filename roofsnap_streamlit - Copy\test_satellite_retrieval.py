#!/usr/bin/env python3
"""
Test Satellite Image Retrieval Functionality
Comprehensive testing of the complete satellite image acquisition pipeline
"""

import os
import sys
import time
import json
from PIL import Image

# Add the current directory to Python path
script_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(script_dir)
os.chdir(script_dir)

from app import EnhancedSolarAnalyzer

def test_image_acquisition_pipeline():
    """Test the complete image acquisition pipeline"""
    print("🛰️ Testing Complete Image Acquisition Pipeline")
    print("-" * 60)
    
    analyzer = EnhancedSolarAnalyzer()
    
    # Test various coordinates around the world
    test_coordinates = [
        {"name": "New York City", "lat": 40.7128, "lng": -74.0060},
        {"name": "Los Angeles", "lat": 34.0522, "lng": -118.2437},
        {"name": "London, UK", "lat": 51.5074, "lng": -0.1278},
        {"name": "Tokyo, Japan", "lat": 35.6762, "lng": 139.6503},
        {"name": "Sydney, Australia", "lat": -33.8688, "lng": 151.2093},
        {"name": "Invalid Coordinates", "lat": 999, "lng": 999},  # Test error handling
    ]
    
    results = {
        'successful_retrievals': 0,
        'failed_retrievals': 0,
        'fallback_usage': 0,
        'nasa_api_usage': 0,
        'error_types': {},
        'image_details': []
    }
    
    for coord in test_coordinates:
        print(f"\n📍 Testing: {coord['name']} ({coord['lat']}, {coord['lng']})")
        
        try:
            result = analyzer.get_satellite_image(coord['lat'], coord['lng'])
            
            print(f"   Success: {result['success']}")
            
            if result['success']:
                results['successful_retrievals'] += 1
                
                # Check source
                source = result.get('source', 'Unknown')
                print(f"   Source: {source}")
                
                if 'NASA' in source:
                    results['nasa_api_usage'] += 1
                elif 'fallback' in source.lower():
                    results['fallback_usage'] += 1
                
                # Validate image file
                image_path = result['image_path']
                print(f"   Image Path: {image_path}")
                
                if os.path.exists(image_path):
                    file_size = os.path.getsize(image_path)
                    print(f"   File Size: {file_size:,} bytes")
                    
                    # Try to open and validate the image
                    try:
                        with Image.open(image_path) as img:
                            width, height = img.size
                            format_type = img.format
                            print(f"   Image Dimensions: {width}x{height}")
                            print(f"   Image Format: {format_type}")
                            
                            results['image_details'].append({
                                'location': coord['name'],
                                'path': image_path,
                                'size': file_size,
                                'dimensions': f"{width}x{height}",
                                'format': format_type,
                                'source': source
                            })
                            
                            print(f"   ✅ Image validation successful")
                    
                    except Exception as e:
                        print(f"   ❌ Image validation failed: {str(e)}")
                        results['error_types']['image_validation'] = results['error_types'].get('image_validation', 0) + 1
                
                else:
                    print(f"   ❌ Image file not found: {image_path}")
                    results['error_types']['file_not_found'] = results['error_types'].get('file_not_found', 0) + 1
                
                # Check for warnings
                if 'warning' in result:
                    print(f"   ⚠️ Warning: {result['warning']}")
            
            else:
                results['failed_retrievals'] += 1
                error = result.get('error', 'Unknown error')
                print(f"   ❌ Error: {error}")
                
                error_type = 'coordinate_validation' if 'coordinate' in error.lower() else 'other'
                results['error_types'][error_type] = results['error_types'].get(error_type, 0) + 1
                
                if 'suggestion' in result:
                    print(f"   💡 Suggestion: {result['suggestion']}")
        
        except Exception as e:
            print(f"   💥 Exception: {str(e)}")
            results['failed_retrievals'] += 1
            results['error_types']['exception'] = results['error_types'].get('exception', 0) + 1
        
        # Small delay between requests
        time.sleep(1)
    
    return results

def test_fallback_system_reliability():
    """Test the reliability of the fallback system"""
    print("\n\n📁 Testing Fallback System Reliability")
    print("-" * 60)
    
    # Check all test images
    test_images = [
        'temp/satellite_single_house.jpg',
        'temp/satellite_professional_highlighted.jpg',
        'temp/test_satellite.jpg'
    ]
    
    fallback_results = {
        'available_images': 0,
        'valid_images': 0,
        'image_details': []
    }
    
    for img_path in test_images:
        print(f"\n🖼️ Testing: {img_path}")
        
        if os.path.exists(img_path):
            fallback_results['available_images'] += 1
            file_size = os.path.getsize(img_path)
            print(f"   ✅ File exists: {file_size:,} bytes")
            
            # Validate image
            try:
                with Image.open(img_path) as img:
                    width, height = img.size
                    format_type = img.format
                    mode = img.mode
                    
                    print(f"   📏 Dimensions: {width}x{height}")
                    print(f"   🎨 Format: {format_type}")
                    print(f"   🌈 Mode: {mode}")
                    
                    # Check if image is reasonable size for satellite imagery
                    if width >= 200 and height >= 200:
                        print(f"   ✅ Image dimensions are suitable")
                        fallback_results['valid_images'] += 1
                    else:
                        print(f"   ⚠️ Image dimensions may be too small")
                    
                    fallback_results['image_details'].append({
                        'path': img_path,
                        'size': file_size,
                        'dimensions': f"{width}x{height}",
                        'format': format_type,
                        'mode': mode,
                        'valid': width >= 200 and height >= 200
                    })
            
            except Exception as e:
                print(f"   ❌ Image validation failed: {str(e)}")
        
        else:
            print(f"   ❌ File not found")
    
    return fallback_results

def test_edge_cases():
    """Test edge cases and error conditions"""
    print("\n\n🚨 Testing Edge Cases and Error Conditions")
    print("-" * 60)
    
    analyzer = EnhancedSolarAnalyzer()
    
    edge_cases = [
        {"name": "Invalid latitude (too high)", "lat": 91, "lng": 0},
        {"name": "Invalid latitude (too low)", "lat": -91, "lng": 0},
        {"name": "Invalid longitude (too high)", "lat": 0, "lng": 181},
        {"name": "Invalid longitude (too low)", "lat": 0, "lng": -181},
        {"name": "String coordinates", "lat": "40.7128", "lng": "-74.0060"},
        {"name": "None coordinates", "lat": None, "lng": None},
        {"name": "Extreme valid coordinates", "lat": 89.9, "lng": 179.9},
    ]
    
    edge_results = {
        'handled_correctly': 0,
        'unexpected_behavior': 0,
        'error_messages': []
    }
    
    for case in edge_cases:
        print(f"\n🧪 Testing: {case['name']}")
        print(f"   Coordinates: {case['lat']}, {case['lng']}")
        
        try:
            result = analyzer.get_satellite_image(case['lat'], case['lng'])
            
            if case['name'].startswith('Invalid') or case['name'].startswith('String') or case['name'].startswith('None'):
                # These should fail
                if not result['success']:
                    print(f"   ✅ Correctly handled: {result['error']}")
                    edge_results['handled_correctly'] += 1
                else:
                    print(f"   ⚠️ Unexpected success")
                    edge_results['unexpected_behavior'] += 1
            else:
                # These should succeed or fail gracefully
                if result['success']:
                    print(f"   ✅ Success: {result.get('source', 'Unknown source')}")
                    edge_results['handled_correctly'] += 1
                else:
                    print(f"   ✅ Graceful failure: {result['error']}")
                    edge_results['handled_correctly'] += 1
            
            if 'error' in result:
                edge_results['error_messages'].append({
                    'case': case['name'],
                    'error': result['error']
                })
        
        except Exception as e:
            print(f"   💥 Unhandled exception: {str(e)}")
            edge_results['unexpected_behavior'] += 1
    
    return edge_results

def main():
    """Run comprehensive satellite image retrieval tests"""
    print("🧪 Satellite Image Retrieval Test Suite")
    print("=" * 70)
    
    # Test 1: Complete acquisition pipeline
    pipeline_results = test_image_acquisition_pipeline()
    
    # Test 2: Fallback system reliability
    fallback_results = test_fallback_system_reliability()
    
    # Test 3: Edge cases
    edge_results = test_edge_cases()
    
    # Summary
    print("\n\n📊 Test Results Summary")
    print("=" * 50)
    
    print(f"Image Acquisition Pipeline:")
    print(f"   Successful retrievals: {pipeline_results['successful_retrievals']}")
    print(f"   Failed retrievals: {pipeline_results['failed_retrievals']}")
    print(f"   NASA API usage: {pipeline_results['nasa_api_usage']}")
    print(f"   Fallback usage: {pipeline_results['fallback_usage']}")
    print(f"   Error types: {pipeline_results['error_types']}")
    
    print(f"\nFallback System:")
    print(f"   Available images: {fallback_results['available_images']}/3")
    print(f"   Valid images: {fallback_results['valid_images']}/3")
    
    print(f"\nEdge Case Handling:")
    print(f"   Correctly handled: {edge_results['handled_correctly']}")
    print(f"   Unexpected behavior: {edge_results['unexpected_behavior']}")
    
    # Overall assessment
    total_tests = pipeline_results['successful_retrievals'] + pipeline_results['failed_retrievals']
    success_rate = (pipeline_results['successful_retrievals'] / total_tests * 100) if total_tests > 0 else 0
    
    print(f"\n🎯 Overall Assessment:")
    print(f"   Success Rate: {success_rate:.1f}%")
    print(f"   Fallback System: {'✅ Reliable' if fallback_results['valid_images'] >= 2 else '⚠️ Limited'}")
    print(f"   Error Handling: {'✅ Robust' if edge_results['unexpected_behavior'] == 0 else '⚠️ Needs improvement'}")
    
    # Save detailed results
    detailed_results = {
        'pipeline': pipeline_results,
        'fallback': fallback_results,
        'edge_cases': edge_results,
        'summary': {
            'success_rate': success_rate,
            'fallback_reliable': fallback_results['valid_images'] >= 2,
            'error_handling_robust': edge_results['unexpected_behavior'] == 0
        }
    }
    
    with open('temp/satellite_test_results.json', 'w') as f:
        json.dump(detailed_results, f, indent=2)
    
    print(f"\n💾 Detailed results saved to: temp/satellite_test_results.json")

if __name__ == "__main__":
    main()
