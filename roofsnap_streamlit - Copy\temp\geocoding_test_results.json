[{"address": "1600 Pennsylvania Avenue NW, Washington, DC 20500", "description": "Full US address with ZIP", "expected": true, "actual": true, "correct": true, "result": {"success": true, "lat": 38.8976997, "lng": -77.0365532, "formatted_address": "White House, 1600, Pennsylvania Avenue Northwest, Ward 2, Washington, District of Columbia, 20500, United States"}}, {"address": "350 Fifth Avenue, New York, NY 10118", "description": "Empire State Building", "expected": true, "actual": true, "correct": true, "result": {"success": true, "lat": 40.9161016, "lng": -73.8071811, "formatted_address": "350, 5th Avenue, North Pelham, Village of Pelham, Town of Pelham, Westchester County, New York, 10803, United States"}}, {"address": "1 Infinite Loop, Cupertino, CA 95014", "description": "Apple headquarters", "expected": true, "actual": true, "correct": true, "result": {"success": true, "lat": 37.3316697, "lng": -122.0300982, "formatted_address": "Infinite Loop 1, 1, Infinite Loop, Apple Campus, Cupertino, Santa Clara County, California, 95014, United States"}}, {"address": "Times Square, New York", "description": "Famous landmark", "expected": true, "actual": true, "correct": true, "result": {"success": true, "lat": 40.7572614, "lng": -73.9858998, "formatted_address": "Times Square, Theater District, Manhattan Community Board 5, Manhattan, New York County, City of New York, New York, 10036, United States"}}, {"address": "Central Park, NYC", "description": "Park with city abbreviation", "expected": true, "actual": true, "correct": true, "result": {"success": true, "lat": 40.7827725, "lng": -73.9653627, "formatted_address": "Central Park, New York County, City of New York, New York, United States"}}, {"address": "Golden Gate Bridge, San Francisco", "description": "Famous bridge", "expected": true, "actual": true, "correct": true, "result": {"success": true, "lat": 37.8202408, "lng": -122.47857, "formatted_address": "Golden Gate Bridge, San Francisco, Marin County, California, 94129, United States"}}, {"address": "10 Downing Street, London, UK", "description": "UK address", "expected": true, "actual": true, "correct": true, "result": {"success": true, "lat": 51.5034878, "lng": -0.1276965, "formatted_address": "10 Downing Street, 10, Downing Street, Westminster, Millbank, London, Greater London, England, SW1A 2AA, United Kingdom"}}, {"address": "Champs-Élysées, Paris, France", "description": "French address", "expected": true, "actual": true, "correct": true, "result": {"success": true, "lat": 48.8707573, "lng": 2.3053312, "formatted_address": "Champs Élysées, Champs-Elysées, Paris, France métropolitaine, 75008, France"}}, {"address": "Sydney Opera House, Australia", "description": "Australian landmark", "expected": true, "actual": true, "correct": true, "result": {"success": true, "lat": -33.8571981, "lng": 151.2151234, "formatted_address": "Sydney Opera House, 2, Macquarie Street, Quay Quarter, Sydney, Sydney CBD, Sydney, Council of the City of Sydney, New South Wales, 2000, Australia"}}, {"address": "Los Angeles, CA", "description": "City and state", "expected": true, "actual": true, "correct": true, "result": {"success": true, "lat": 34.0536909, "lng": -118.242766, "formatted_address": "Los Angeles, Los Angeles County, California, United States"}}, {"address": "Miami, Florida", "description": "City and full state name", "expected": true, "actual": true, "correct": true, "result": {"success": true, "lat": 25.7741728, "lng": -80.19362, "formatted_address": "Miami, Miami-Dade County, Florida, United States"}}, {"address": "123 Fake Street, Nowhere, XX", "description": "Non-existent address", "expected": false, "actual": false, "correct": true, "result": {"success": false, "error": "No results found for the given address"}}, {"address": "Atlantis, Lost City", "description": "Fictional location", "expected": false, "actual": false, "correct": true, "result": {"success": false, "error": "No results found for the given address"}}, {"address": "", "description": "Empty string", "expected": false, "actual": false, "correct": true, "result": {"success": false, "error": "Address must be a non-empty string"}}, {"address": "   ", "description": "Whitespace only", "expected": false, "actual": false, "correct": true, "result": {"success": false, "error": "Geocoding service unavailable", "details": "400 Client Error: Bad Request for url: https://nominatim.openstreetmap.org/search?q=+++&format=json&limit=1&addressdetails=1"}}, {"address": "12345", "description": "Numbers only", "expected": false, "actual": true, "correct": false, "result": {"success": true, "lat": 29.7747193, "lng": 32.0603309, "formatted_address": "12345, السويس, مصر"}}]