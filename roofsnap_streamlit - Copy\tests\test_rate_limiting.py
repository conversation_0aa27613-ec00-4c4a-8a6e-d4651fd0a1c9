import unittest
import time
from unittest.mock import MagicMock
import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
from app import EnhancedSolarAnalyzer

# Mock Streamlit components
import sys
from unittest.mock import patch

# Create complete Streamlit mock
st_mock = MagicMock()
st_mock.runtime = MagicMock()
st_mock.runtime.scriptrunner_utils = MagicMock()
sys.modules['streamlit'] = st_mock

# Patch Streamlit imports
patcher = patch.dict('sys.modules', {
    'streamlit': st_mock,
    'streamlit.runtime': st_mock.runtime,
    'streamlit.runtime.scriptrunner_utils': st_mock.runtime.scriptrunner_utils
})
patcher.start()

class TestRateLimiting(unittest.TestCase):
    """Test cases for API rate limiting functionality"""
    
    def setUp(self):
        self.analyzer = EnhancedSolarAnalyzer()
        # Initialize required API keys for testing
        self.analyzer.nasa_api_key = "test_key"
        self.analyzer.gemini_api_key = "test_key"
        self.analyzer.google_api_key = "test_key"
        # Reset rate limits for clean test state
        for api in self.analyzer.api_limits.values():
            api['call_count'] = 0
            api['last_called'] = 0
        
    def test_nasa_rate_limiting(self):
        """Verify NASA API rate limits are enforced"""
        # Stay within limit (10 calls)
        for _ in range(10):
            result = self.analyzer.get_satellite_image(37.7, -122.4)
            self.assertTrue(result['success'])
            
        # 11th call should be rate limited
        result = self.analyzer.get_satellite_image(37.7, -122.4)
        self.assertFalse(result['success'])
        self.assertEqual(result['error'], 'NASA API rate limit exceeded')
        self.assertIn('retry_after', result)
        
    def test_gemini_rate_limiting(self):
        """Verify Gemini API rate limits are enforced"""
        # Stay within limit (5 calls)
        for _ in range(5):
            result = self.analyzer.analyze_with_gemini(
                "temp/satellite_single_house.jpg",
                "123 Main St",
                {"coordinates": {"lat": 37.7, "lng": -122.4}}
            )
            self.assertTrue(result['success'])
            
        # 6th call should be rate limited
        result = self.analyzer.analyze_with_gemini(
            "temp/satellite_single_house.jpg",
            "123 Main St",
            {"coordinates": {"lat": 37.7, "lng": -122.4}}
        )
        self.assertFalse(result['success'])
        self.assertEqual(result['error'], 'Gemini API rate limit exceeded')
        self.assertIn('retry_after', result)
        
    def setUp(self):
        """Initialize test environment with proper mocks"""
        import os
        from unittest.mock import patch
        
        # Create analyzer instance with test keys
        self.analyzer = EnhancedSolarAnalyzer()
        self.analyzer.nasa_api_key = "test_key"
        self.analyzer.gemini_api_key = "test_key"
        self.analyzer.google_api_key = "test_key"
        
        # Reset rate limits
        for api in self.analyzer.api_limits.values():
            api['call_count'] = 0
            api['last_called'] = 0
            
        # Setup test directory
        self.test_dir = 'temp_test'
        os.makedirs(self.test_dir, exist_ok=True)
        
        # Mock file operations for all tests
        self.file_patcher = patch('builtins.open', unittest.mock.mock_open())
        self.file_patcher.start()
        self.path_patcher = patch('os.path.exists', return_value=True)
        self.path_patcher.start()
        
    def tearDown(self):
        """Clean up test environment"""
        import shutil
        self.file_patcher.stop()
        self.path_patcher.stop()
        if os.path.exists(self.test_dir):
            shutil.rmtree(self.test_dir)

    def tearDown(self):
        """Clean up test files"""
        import shutil
        if os.path.exists(self.test_dir):
            shutil.rmtree(self.test_dir)

    def test_google_rate_limiting(self):
        """Verify Google Maps/Solar API rate limits are enforced"""
        # Enhanced mock configuration
        mock_file_data = {
            'temp/street_view_front.jpg': b'\xff\xd8\xff\xe0\x00\x10JFIF',
            'temp/street_view_back.jpg': b'\xff\xd8\xff\xe0\x00\x10JFIF',
            'temp/street_view_left.jpg': b'\xff\xd8\xff\xe0\x00\x10JFIF',
            'temp/street_view_right.jpg': b'\xff\xd8\xff\xe0\x00\x10JFIF'
        }
        
        def mock_file_open(path, *args, **kwargs):
            if path in mock_file_data:
                return unittest.mock.mock_open(read_data=mock_file_data[path])(path, *args, **kwargs)
            return unittest.mock.mock_open()(path, *args, **kwargs)
            
        with patch('os.path.exists', side_effect=lambda x: x in mock_file_data), \
             patch('builtins.open', new=mock_file_open), \
             patch('os.makedirs'):
            
            # Stay within limit (15 calls)
            for _ in range(15):
                result = self.analyzer.get_street_views_all_angles(37.7, -122.4)
                self.assertTrue(isinstance(result, dict))
                self.assertTrue('front' in result)
            
        # 16th call should be rate limited
        result = self.analyzer.get_street_views_all_angles(37.7, -122.4)
        self.assertFalse(result['success'])
        self.assertEqual(result['error'], 'Google Maps API rate limit exceeded')
        self.assertIn('retry_after', result)
        
    def test_rate_limit_reset(self):
        """Verify rate limits reset after period elapses"""
        # Exceed NASA API limit
        for _ in range(11):
            self.analyzer.get_satellite_image(37.7, -122.4)
            
        # Verify rate limited
        result = self.analyzer.get_satellite_image(37.7, -122.4)
        self.assertFalse(result['success'])
        
        # Wait for reset (mock time in actual tests)
        self.analyzer.api_limits['nasa']['last_called'] = 0
        
        # Verify limit reset
        result = self.analyzer.get_satellite_image(37.7, -122.4)
        self.assertTrue(result['success'])

if __name__ == '__main__':
    unittest.main()