Solar Analysis System Log
========================================
{"timestamp": "2025-07-05 19:32:17", "type": "CONFIGURATION", "message": "Gemini API key not found", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}}
{"timestamp": "2025-07-05 19:32:18", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 1}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:32:18", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 2}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:32:18", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 3}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:32:18", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 4}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:32:18", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 5}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:32:18", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 6}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:32:18", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 7}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:32:18", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 8}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:32:18", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 9}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:32:18", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 10}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:32:18", "type": "RATE_LIMIT", "message": "API rate limit exceeded for nasa", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 11}, "limit": 10, "period": 60}
{"timestamp": "2025-07-05 19:32:43", "type": "CONFIGURATION", "message": "Gemini API key not found", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}}
{"timestamp": "2025-07-05 19:32:43", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 1}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:33:59", "type": "CONFIGURATION", "message": "Gemini API key not found", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}}
{"timestamp": "2025-07-05 19:34:02", "type": "CONFIGURATION", "message": "Gemini API key not found", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}}
{"timestamp": "2025-07-05 19:34:02", "type": "CONFIGURATION", "message": "Gemini API key not found", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}}
{"timestamp": "2025-07-05 19:34:02", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 1}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:02", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 2}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:02", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 3}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:02", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 4}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:02", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 5}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:02", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 6}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:02", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 7}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:02", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 8}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:02", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 9}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:02", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 10}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:02", "type": "RATE_LIMIT", "message": "API rate limit exceeded for nasa", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 11}, "limit": 10, "period": 60}
{"timestamp": "2025-07-05 19:34:02", "type": "CONFIGURATION", "message": "Gemini API key not found", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}}
{"timestamp": "2025-07-05 19:34:02", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 1}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:02", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 2}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:02", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 3}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:02", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 4}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:02", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 5}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:02", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 6}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:02", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 7}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:02", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 8}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:02", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 9}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:02", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 10}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:02", "type": "RATE_LIMIT", "message": "API rate limit exceeded for nasa", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 11}, "limit": 10, "period": 60}
{"timestamp": "2025-07-05 19:34:02", "type": "RATE_LIMIT", "message": "API rate limit exceeded for nasa", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 12}, "limit": 10, "period": 60}
{"timestamp": "2025-07-05 19:34:02", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 13}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:28", "type": "CONFIGURATION", "message": "Gemini API key not found", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}}
{"timestamp": "2025-07-05 19:34:28", "type": "CONFIGURATION", "message": "Gemini API key not found", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}}
{"timestamp": "2025-07-05 19:34:28", "type": "CONFIGURATION", "message": "Gemini API key not found", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}}
{"timestamp": "2025-07-05 19:34:28", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 1}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:28", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 2}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:28", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 3}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:28", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 4}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:28", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 5}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:28", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 6}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:28", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 7}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:28", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 8}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:28", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 9}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:28", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 10}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:28", "type": "RATE_LIMIT", "message": "API rate limit exceeded for nasa", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 11}, "limit": 10, "period": 60}
{"timestamp": "2025-07-05 19:34:28", "type": "CONFIGURATION", "message": "Gemini API key not found", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}}
{"timestamp": "2025-07-05 19:34:28", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 1}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:28", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 2}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:28", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 3}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:28", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 4}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:28", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 5}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:28", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 6}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:28", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 7}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:28", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 8}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:28", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 9}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:28", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 10}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:28", "type": "RATE_LIMIT", "message": "API rate limit exceeded for nasa", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 11}, "limit": 10, "period": 60}
{"timestamp": "2025-07-05 19:34:28", "type": "RATE_LIMIT", "message": "API rate limit exceeded for nasa", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 12}, "limit": 10, "period": 60}
{"timestamp": "2025-07-05 19:34:28", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 13}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:53", "type": "CONFIGURATION", "message": "Gemini API key not found", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}}
{"timestamp": "2025-07-05 19:34:53", "type": "CONFIGURATION", "message": "Gemini API key not found", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}}
{"timestamp": "2025-07-05 19:34:54", "type": "CONFIGURATION", "message": "Gemini API key not found", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}}
{"timestamp": "2025-07-05 19:34:54", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 1}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:54", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 2}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:54", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 3}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:54", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 4}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:54", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 5}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:54", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 6}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:54", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 7}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:54", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 8}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:54", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 9}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:54", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 10}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:54", "type": "RATE_LIMIT", "message": "API rate limit exceeded for nasa", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 11}, "limit": 10, "period": 60}
{"timestamp": "2025-07-05 19:34:54", "type": "CONFIGURATION", "message": "Gemini API key not found", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}}
{"timestamp": "2025-07-05 19:34:54", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 1}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:54", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 2}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:54", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 3}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:54", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 4}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:54", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 5}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:54", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 6}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:54", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 7}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:54", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 8}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:54", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 9}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:54", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 10}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:54", "type": "RATE_LIMIT", "message": "API rate limit exceeded for nasa", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 11}, "limit": 10, "period": 60}
{"timestamp": "2025-07-05 19:34:54", "type": "RATE_LIMIT", "message": "API rate limit exceeded for nasa", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 12}, "limit": 10, "period": 60}
{"timestamp": "2025-07-05 19:34:54", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 13}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:35:36", "type": "CONFIGURATION", "message": "Gemini API key not found", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}}
{"timestamp": "2025-07-05 19:35:36", "type": "CONFIGURATION", "message": "Gemini API key not found", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}}
{"timestamp": "2025-07-05 19:35:37", "type": "CONFIGURATION", "message": "Gemini API key not found", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}}
{"timestamp": "2025-07-05 19:35:37", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 1}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:35:37", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 2}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:35:37", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 3}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:35:37", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 4}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:35:37", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 5}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:35:37", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 6}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:35:37", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 7}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:35:37", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 8}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:35:37", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 9}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:35:37", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 10}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:35:37", "type": "RATE_LIMIT", "message": "API rate limit exceeded for nasa", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 11}, "limit": 10, "period": 60}
{"timestamp": "2025-07-05 19:35:37", "type": "CONFIGURATION", "message": "Gemini API key not found", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}}
{"timestamp": "2025-07-05 19:35:37", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 1}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:35:37", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 2}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:35:37", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 3}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:35:37", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 4}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:35:37", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 5}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:35:37", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 6}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:35:37", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 7}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:35:37", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 8}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:35:37", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 9}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:35:37", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 10}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:35:37", "type": "RATE_LIMIT", "message": "API rate limit exceeded for nasa", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 11}, "limit": 10, "period": 60}
{"timestamp": "2025-07-05 19:35:37", "type": "RATE_LIMIT", "message": "API rate limit exceeded for nasa", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 12}, "limit": 10, "period": 60}
{"timestamp": "2025-07-05 19:35:37", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 13}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:36:07", "type": "CONFIGURATION", "message": "Gemini API key not found", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}}
{"timestamp": "2025-07-05 19:36:07", "type": "CONFIGURATION", "message": "Gemini API key not found", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}}
{"timestamp": "2025-07-05 19:36:08", "type": "CONFIGURATION", "message": "Gemini API key not found", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}}
{"timestamp": "2025-07-05 19:36:08", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 1}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:36:08", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 2}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:36:08", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 3}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:36:08", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 4}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:36:08", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 5}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:36:08", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 6}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:36:08", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 7}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:36:08", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 8}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:36:08", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 9}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:36:08", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 10}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:36:08", "type": "RATE_LIMIT", "message": "API rate limit exceeded for nasa", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 11}, "limit": 10, "period": 60}
{"timestamp": "2025-07-05 19:36:08", "type": "CONFIGURATION", "message": "Gemini API key not found", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}}
{"timestamp": "2025-07-05 19:36:08", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 1}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:36:08", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 2}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:36:08", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 3}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:36:08", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 4}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:36:08", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 5}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:36:08", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 6}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:36:08", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 7}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:36:08", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 8}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:36:08", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 9}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:36:08", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 10}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:36:08", "type": "RATE_LIMIT", "message": "API rate limit exceeded for nasa", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 11}, "limit": 10, "period": 60}
{"timestamp": "2025-07-05 19:36:08", "type": "RATE_LIMIT", "message": "API rate limit exceeded for nasa", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 12}, "limit": 10, "period": 60}
{"timestamp": "2025-07-05 19:36:08", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 13}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:36:52", "type": "CONFIGURATION", "message": "Gemini API key not found", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}}
{"timestamp": "2025-07-05 19:36:53", "type": "CONFIGURATION", "message": "Gemini API key not found", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}}
{"timestamp": "2025-07-05 19:36:53", "type": "CONFIGURATION", "message": "Gemini API key not found", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}}
{"timestamp": "2025-07-05 19:36:53", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 1}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:36:53", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 2}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:36:53", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 3}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:36:53", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 4}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:36:53", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 5}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:36:53", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 6}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:36:53", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 7}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:36:53", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 8}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:36:53", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 9}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:36:53", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 10}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:36:53", "type": "RATE_LIMIT", "message": "API rate limit exceeded for nasa", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 11}, "limit": 10, "period": 60}
{"timestamp": "2025-07-05 19:36:54", "type": "CONFIGURATION", "message": "Gemini API key not found", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}}
{"timestamp": "2025-07-05 19:36:54", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 1}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:36:54", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 2}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:36:54", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 3}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:36:54", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 4}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:36:54", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 5}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:36:54", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 6}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:36:54", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 7}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:36:54", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 8}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:36:54", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 9}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:36:54", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 10}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:36:54", "type": "RATE_LIMIT", "message": "API rate limit exceeded for nasa", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 11}, "limit": 10, "period": 60}
{"timestamp": "2025-07-05 19:36:54", "type": "RATE_LIMIT", "message": "API rate limit exceeded for nasa", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 12}, "limit": 10, "period": 60}
{"timestamp": "2025-07-05 19:36:54", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 13}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:38:42", "type": "CONFIGURATION", "message": "Gemini API key not found", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}}
{"timestamp": "2025-07-05 19:38:43", "type": "CONFIGURATION", "message": "Gemini API key not found", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}}
{"timestamp": "2025-07-05 19:40:45", "type": "CONFIGURATION", "message": "Gemini API key not found", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}}
{"timestamp": "2025-07-05 19:40:45", "type": "CONFIGURATION", "message": "Gemini API key not found", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}}
{"timestamp": "2025-07-05 19:42:02", "type": "CONFIGURATION", "message": "Gemini API key not found", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}}
{"timestamp": "2025-07-05 19:42:02", "type": "CONFIGURATION", "message": "Gemini API key not found", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}}
{"timestamp": "2025-07-05 19:42:49", "type": "CONFIGURATION", "message": "Gemini API key not found", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}}
{"timestamp": "2025-07-05 19:42:50", "type": "CONFIGURATION", "message": "Gemini API key not found", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}}
{"timestamp": "2025-07-05 19:43:25", "type": "CONFIGURATION", "message": "Gemini API key not found", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}}
{"timestamp": "2025-07-05 19:43:25", "type": "CONFIGURATION", "message": "Gemini API key not found", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}}
{"timestamp": "2025-07-05 19:43:54", "type": "CONFIGURATION", "message": "Gemini API key not found", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}}
{"timestamp": "2025-07-05 19:43:54", "type": "CONFIGURATION", "message": "Gemini API key not found", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}}
{"timestamp": "2025-07-05 19:44:26", "type": "CONFIGURATION", "message": "Gemini API key not found", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}}
{"timestamp": "2025-07-05 19:44:26", "type": "CONFIGURATION", "message": "Gemini API key not found", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}}
{"timestamp": "2025-07-05 19:45:35", "type": "CONFIGURATION", "message": "Gemini API key not found", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}}
{"timestamp": "2025-07-05 19:45:35", "type": "CONFIGURATION", "message": "Gemini API key not found", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}}
{"timestamp": "2025-07-05 19:49:14", "type": "CONFIGURATION", "message": "Gemini API key not found", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}}
{"timestamp": "2025-07-05 19:49:26", "type": "CONFIGURATION", "message": "Gemini API key not found", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}}
{"timestamp": "2025-07-05 19:52:29", "type": "CONFIGURATION", "message": "Gemini API key not found", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}}
{"timestamp": "2025-07-05 19:54:19", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:58:03", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 21:18:52", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=37.4224857, lng=-122.0855846, date=2025-06-05", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 37.4224857, "lon": -122.0855846, "date": "2025-06-05", "dim": 0.15}}
{"timestamp": "2025-07-05 21:18:54", "type": "API_ERROR", "message": "NASA API error for date 2025-06-05: 500", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 1}, "response": "<!DOCTYPE HTML PUBLIC \"-//IETF//DTD HTML 2.0//EN\">\n<html><head>\n<title>500 Internal Server Error</title>\n</head><body>\n<h1>Internal Server Error</h1>\n<p>The server encountered an internal error or\nmis"}
{"timestamp": "2025-07-05 21:18:55", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=37.4224857, lng=-122.0855846, date=2025-05-06", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 2}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 37.4224857, "lon": -122.0855846, "date": "2025-05-06", "dim": 0.15}}
{"timestamp": "2025-07-05 21:18:57", "type": "API_ERROR", "message": "NASA API error for date 2025-05-06: 500", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 3}, "response": "<!DOCTYPE HTML PUBLIC \"-//IETF//DTD HTML 2.0//EN\">\n<html><head>\n<title>500 Internal Server Error</title>\n</head><body>\n<h1>Internal Server Error</h1>\n<p>The server encountered an internal error or\nmis"}
{"timestamp": "2025-07-05 21:18:58", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=37.4224857, lng=-122.0855846, date=2025-04-06", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 4}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 37.4224857, "lon": -122.0855846, "date": "2025-04-06", "dim": 0.15}}
{"timestamp": "2025-07-05 21:19:00", "type": "API_ERROR", "message": "NASA API error for date 2025-04-06: 500", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 5}, "response": "<!DOCTYPE HTML PUBLIC \"-//IETF//DTD HTML 2.0//EN\">\n<html><head>\n<title>500 Internal Server Error</title>\n</head><body>\n<h1>Internal Server Error</h1>\n<p>The server encountered an internal error or\nmis"}
{"timestamp": "2025-07-05 21:19:00", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=37.4224857, lng=-122.0855846, date=2023-06-01", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 6}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 37.4224857, "lon": -122.0855846, "date": "2023-06-01", "dim": 0.15}}
{"timestamp": "2025-07-05 21:19:02", "type": "API_ERROR", "message": "NASA API error for date 2023-06-01: 500", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 7}, "response": "<!DOCTYPE HTML PUBLIC \"-//IETF//DTD HTML 2.0//EN\">\n<html><head>\n<title>500 Internal Server Error</title>\n</head><body>\n<h1>Internal Server Error</h1>\n<p>The server encountered an internal error or\nmis"}
{"timestamp": "2025-07-05 21:19:03", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=37.4224857, lng=-122.0855846, date=2023-01-01", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 8}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 37.4224857, "lon": -122.0855846, "date": "2023-01-01", "dim": 0.15}}
{"timestamp": "2025-07-05 21:19:05", "type": "API_ERROR", "message": "NASA API error for date 2023-01-01: 500", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 9}, "response": "<!DOCTYPE HTML PUBLIC \"-//IETF//DTD HTML 2.0//EN\">\n<html><head>\n<title>500 Internal Server Error</title>\n</head><body>\n<h1>Internal Server Error</h1>\n<p>The server encountered an internal error or\nmis"}
{"timestamp": "2025-07-05 21:19:06", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 10}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 21:24:47", "type": "INPUT_VALIDATION", "message": "Invalid address input", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}}
{"timestamp": "2025-07-05 21:24:49", "type": "GEOCODING", "message": "Geocoding API error: 400 Client Error: Bad Request for url: https://nominatim.openstreetmap.org/search?q=+++&format=json&limit=1&addressdetails=1", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 1}}
{"timestamp": "2025-07-05 21:24:50", "type": "INPUT_VALIDATION", "message": "Invalid address input", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}}
{"timestamp": "2025-07-05 21:24:50", "type": "INPUT_VALIDATION", "message": "Invalid address input", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 1}}
{"timestamp": "2025-07-05 21:24:50", "type": "INPUT_VALIDATION", "message": "Invalid address input", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 2}}
{"timestamp": "2025-07-05 21:24:50", "type": "INPUT_VALIDATION", "message": "Invalid address input", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 3}}
{"timestamp": "2025-07-05 21:24:50", "type": "INPUT_VALIDATION", "message": "Invalid address input", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 4}}
{"timestamp": "2025-07-05 21:24:50", "type": "INPUT_VALIDATION", "message": "Invalid address input", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 5}}
{"timestamp": "2025-07-05 21:28:20", "type": "INPUT_VALIDATION", "message": "Invalid address input", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}}
{"timestamp": "2025-07-05 21:28:21", "type": "NO_RESULTS", "message": "No results from Nominatim for: 123 Fake Street, Nowhere", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 1}}
{"timestamp": "2025-07-05 21:28:21", "type": "ALTERNATIVE", "message": "Trying alternative format: 123 Fake Street  Nowhere", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 2}}
{"timestamp": "2025-07-05 21:28:22", "type": "NO_RESULTS", "message": "No results from Nominatim for: 123 Fake Street  Nowhere", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 3}}
{"timestamp": "2025-07-05 21:28:22", "type": "ALTERNATIVE", "message": "Trying alternative format: 123 Fake Street", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 4}}
{"timestamp": "2025-07-05 21:28:24", "type": "NO_RESULTS", "message": "No results from Nominatim for: Test Address", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 5}}
{"timestamp": "2025-07-05 21:28:24", "type": "ALTERNATIVE", "message": "Trying alternative format: Test Address, USA", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 6}}
{"timestamp": "2025-07-05 21:28:25", "type": "NO_RESULTS", "message": "No results from Nominatim for: Test Address, USA", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 7}}
{"timestamp": "2025-07-05 22:00:22", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=40.7128, lng=-74.006, date=2025-06-05", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 40.7128, "lon": -74.006, "date": "2025-06-05", "dim": 0.15}}
{"timestamp": "2025-07-05 22:00:23", "type": "API_ERROR", "message": "NASA API error for date 2025-06-05: 429", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 1}, "response": "{\n  \"error\": {\n    \"code\": \"OVER_RATE_LIMIT\",\n    \"message\": \"You have exceeded your rate limit. Try again later or contact us at https://api.nasa.gov:443/contact/ for assistance\"\n  }\n}"}
{"timestamp": "2025-07-05 22:00:23", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=40.7128, lng=-74.006, date=2025-05-06", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 2}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 40.7128, "lon": -74.006, "date": "2025-05-06", "dim": 0.15}}
{"timestamp": "2025-07-05 22:00:24", "type": "API_ERROR", "message": "NASA API error for date 2025-05-06: 429", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 3}, "response": "{\n  \"error\": {\n    \"code\": \"OVER_RATE_LIMIT\",\n    \"message\": \"You have exceeded your rate limit. Try again later or contact us at https://api.nasa.gov:443/contact/ for assistance\"\n  }\n}"}
{"timestamp": "2025-07-05 22:00:25", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=40.7128, lng=-74.006, date=2025-04-06", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 4}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 40.7128, "lon": -74.006, "date": "2025-04-06", "dim": 0.15}}
{"timestamp": "2025-07-05 22:00:26", "type": "API_ERROR", "message": "NASA API error for date 2025-04-06: 429", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 5}, "response": "{\n  \"error\": {\n    \"code\": \"OVER_RATE_LIMIT\",\n    \"message\": \"You have exceeded your rate limit. Try again later or contact us at https://api.nasa.gov:443/contact/ for assistance\"\n  }\n}"}
{"timestamp": "2025-07-05 22:00:26", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=40.7128, lng=-74.006, date=2023-06-01", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 6}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 40.7128, "lon": -74.006, "date": "2023-06-01", "dim": 0.15}}
{"timestamp": "2025-07-05 22:00:28", "type": "API_ERROR", "message": "NASA API error for date 2023-06-01: 429", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 7}, "response": "{\n  \"error\": {\n    \"code\": \"OVER_RATE_LIMIT\",\n    \"message\": \"You have exceeded your rate limit. Try again later or contact us at https://api.nasa.gov:443/contact/ for assistance\"\n  }\n}"}
{"timestamp": "2025-07-05 22:00:28", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=40.7128, lng=-74.006, date=2023-01-01", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 8}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 40.7128, "lon": -74.006, "date": "2023-01-01", "dim": 0.15}}
{"timestamp": "2025-07-05 22:00:29", "type": "API_ERROR", "message": "NASA API error for date 2023-01-01: 429", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 9}, "response": "{\n  \"error\": {\n    \"code\": \"OVER_RATE_LIMIT\",\n    \"message\": \"You have exceeded your rate limit. Try again later or contact us at https://api.nasa.gov:443/contact/ for assistance\"\n  }\n}"}
{"timestamp": "2025-07-05 22:00:30", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 10}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 22:00:32", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=34.0522, lng=-118.2437, date=2025-06-05", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 11}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 34.0522, "lon": -118.2437, "date": "2025-06-05", "dim": 0.15}}
{"timestamp": "2025-07-05 22:00:33", "type": "API_ERROR", "message": "NASA API error for date 2025-06-05: 429", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 12}, "response": "{\n  \"error\": {\n    \"code\": \"OVER_RATE_LIMIT\",\n    \"message\": \"You have exceeded your rate limit. Try again later or contact us at https://api.nasa.gov:443/contact/ for assistance\"\n  }\n}"}
{"timestamp": "2025-07-05 22:00:33", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=34.0522, lng=-118.2437, date=2025-05-06", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 13}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 34.0522, "lon": -118.2437, "date": "2025-05-06", "dim": 0.15}}
{"timestamp": "2025-07-05 22:00:35", "type": "API_ERROR", "message": "NASA API error for date 2025-05-06: 429", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 14}, "response": "{\n  \"error\": {\n    \"code\": \"OVER_RATE_LIMIT\",\n    \"message\": \"You have exceeded your rate limit. Try again later or contact us at https://api.nasa.gov:443/contact/ for assistance\"\n  }\n}"}
{"timestamp": "2025-07-05 22:00:35", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=34.0522, lng=-118.2437, date=2025-04-06", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 15}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 34.0522, "lon": -118.2437, "date": "2025-04-06", "dim": 0.15}}
{"timestamp": "2025-07-05 22:00:36", "type": "API_ERROR", "message": "NASA API error for date 2025-04-06: 429", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 16}, "response": "{\n  \"error\": {\n    \"code\": \"OVER_RATE_LIMIT\",\n    \"message\": \"You have exceeded your rate limit. Try again later or contact us at https://api.nasa.gov:443/contact/ for assistance\"\n  }\n}"}
{"timestamp": "2025-07-05 22:00:37", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=34.0522, lng=-118.2437, date=2023-06-01", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 17}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 34.0522, "lon": -118.2437, "date": "2023-06-01", "dim": 0.15}}
{"timestamp": "2025-07-05 22:00:38", "type": "API_ERROR", "message": "NASA API error for date 2023-06-01: 429", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 18}, "response": "{\n  \"error\": {\n    \"code\": \"OVER_RATE_LIMIT\",\n    \"message\": \"You have exceeded your rate limit. Try again later or contact us at https://api.nasa.gov:443/contact/ for assistance\"\n  }\n}"}
{"timestamp": "2025-07-05 22:00:38", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=34.0522, lng=-118.2437, date=2023-01-01", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 19}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 34.0522, "lon": -118.2437, "date": "2023-01-01", "dim": 0.15}}
{"timestamp": "2025-07-05 22:00:39", "type": "API_ERROR", "message": "NASA API error for date 2023-01-01: 429", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 20}, "response": "{\n  \"error\": {\n    \"code\": \"OVER_RATE_LIMIT\",\n    \"message\": \"You have exceeded your rate limit. Try again later or contact us at https://api.nasa.gov:443/contact/ for assistance\"\n  }\n}"}
{"timestamp": "2025-07-05 22:00:40", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 21}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 22:01:54", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=40.7128, lng=-74.006, date=2025-06-05", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 40.7128, "lon": -74.006, "date": "2025-06-05", "dim": 0.15}}
{"timestamp": "2025-07-05 22:01:55", "type": "API_ERROR", "message": "NASA API error for date 2025-06-05: 429", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 1}, "response": "{\n  \"error\": {\n    \"code\": \"OVER_RATE_LIMIT\",\n    \"message\": \"You have exceeded your rate limit. Try again later or contact us at https://api.nasa.gov:443/contact/ for assistance\"\n  }\n}"}
{"timestamp": "2025-07-05 22:01:56", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=40.7128, lng=-74.006, date=2025-05-06", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 2}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 40.7128, "lon": -74.006, "date": "2025-05-06", "dim": 0.15}}
{"timestamp": "2025-07-05 22:01:57", "type": "API_ERROR", "message": "NASA API error for date 2025-05-06: 429", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 3}, "response": "{\n  \"error\": {\n    \"code\": \"OVER_RATE_LIMIT\",\n    \"message\": \"You have exceeded your rate limit. Try again later or contact us at https://api.nasa.gov:443/contact/ for assistance\"\n  }\n}"}
{"timestamp": "2025-07-05 22:01:57", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=40.7128, lng=-74.006, date=2025-04-06", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 4}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 40.7128, "lon": -74.006, "date": "2025-04-06", "dim": 0.15}}
{"timestamp": "2025-07-05 22:01:58", "type": "API_ERROR", "message": "NASA API error for date 2025-04-06: 429", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 5}, "response": "{\n  \"error\": {\n    \"code\": \"OVER_RATE_LIMIT\",\n    \"message\": \"You have exceeded your rate limit. Try again later or contact us at https://api.nasa.gov:443/contact/ for assistance\"\n  }\n}"}
{"timestamp": "2025-07-05 22:01:59", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=40.7128, lng=-74.006, date=2023-06-01", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 6}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 40.7128, "lon": -74.006, "date": "2023-06-01", "dim": 0.15}}
{"timestamp": "2025-07-05 22:02:00", "type": "API_ERROR", "message": "NASA API error for date 2023-06-01: 429", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 7}, "response": "{\n  \"error\": {\n    \"code\": \"OVER_RATE_LIMIT\",\n    \"message\": \"You have exceeded your rate limit. Try again later or contact us at https://api.nasa.gov:443/contact/ for assistance\"\n  }\n}"}
{"timestamp": "2025-07-05 22:02:01", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=40.7128, lng=-74.006, date=2023-01-01", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 8}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 40.7128, "lon": -74.006, "date": "2023-01-01", "dim": 0.15}}
{"timestamp": "2025-07-05 22:02:02", "type": "API_ERROR", "message": "NASA API error for date 2023-01-01: 429", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 9}, "response": "{\n  \"error\": {\n    \"code\": \"OVER_RATE_LIMIT\",\n    \"message\": \"You have exceeded your rate limit. Try again later or contact us at https://api.nasa.gov:443/contact/ for assistance\"\n  }\n}"}
{"timestamp": "2025-07-05 22:02:02", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 10}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 22:02:03", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=34.0522, lng=-118.2437, date=2025-06-05", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 11}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 34.0522, "lon": -118.2437, "date": "2025-06-05", "dim": 0.15}}
{"timestamp": "2025-07-05 22:02:04", "type": "API_ERROR", "message": "NASA API error for date 2025-06-05: 429", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 12}, "response": "{\n  \"error\": {\n    \"code\": \"OVER_RATE_LIMIT\",\n    \"message\": \"You have exceeded your rate limit. Try again later or contact us at https://api.nasa.gov:443/contact/ for assistance\"\n  }\n}"}
{"timestamp": "2025-07-05 22:02:05", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=34.0522, lng=-118.2437, date=2025-05-06", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 13}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 34.0522, "lon": -118.2437, "date": "2025-05-06", "dim": 0.15}}
{"timestamp": "2025-07-05 22:02:06", "type": "API_ERROR", "message": "NASA API error for date 2025-05-06: 429", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 14}, "response": "{\n  \"error\": {\n    \"code\": \"OVER_RATE_LIMIT\",\n    \"message\": \"You have exceeded your rate limit. Try again later or contact us at https://api.nasa.gov:443/contact/ for assistance\"\n  }\n}"}
{"timestamp": "2025-07-05 22:02:06", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=34.0522, lng=-118.2437, date=2025-04-06", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 15}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 34.0522, "lon": -118.2437, "date": "2025-04-06", "dim": 0.15}}
{"timestamp": "2025-07-05 22:02:08", "type": "API_ERROR", "message": "NASA API error for date 2025-04-06: 429", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 16}, "response": "{\n  \"error\": {\n    \"code\": \"OVER_RATE_LIMIT\",\n    \"message\": \"You have exceeded your rate limit. Try again later or contact us at https://api.nasa.gov:443/contact/ for assistance\"\n  }\n}"}
{"timestamp": "2025-07-05 22:02:08", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=34.0522, lng=-118.2437, date=2023-06-01", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 17}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 34.0522, "lon": -118.2437, "date": "2023-06-01", "dim": 0.15}}
{"timestamp": "2025-07-05 22:02:09", "type": "API_ERROR", "message": "NASA API error for date 2023-06-01: 429", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 18}, "response": "{\n  \"error\": {\n    \"code\": \"OVER_RATE_LIMIT\",\n    \"message\": \"You have exceeded your rate limit. Try again later or contact us at https://api.nasa.gov:443/contact/ for assistance\"\n  }\n}"}
{"timestamp": "2025-07-05 22:02:10", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=34.0522, lng=-118.2437, date=2023-01-01", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 19}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 34.0522, "lon": -118.2437, "date": "2023-01-01", "dim": 0.15}}
{"timestamp": "2025-07-05 22:02:11", "type": "API_ERROR", "message": "NASA API error for date 2023-01-01: 429", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 20}, "response": "{\n  \"error\": {\n    \"code\": \"OVER_RATE_LIMIT\",\n    \"message\": \"You have exceeded your rate limit. Try again later or contact us at https://api.nasa.gov:443/contact/ for assistance\"\n  }\n}"}
{"timestamp": "2025-07-05 22:02:11", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 21}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 22:02:12", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=51.5074, lng=-0.1278, date=2025-06-05", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 22}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 51.5074, "lon": -0.1278, "date": "2025-06-05", "dim": 0.15}}
{"timestamp": "2025-07-05 22:02:13", "type": "API_ERROR", "message": "NASA API error for date 2025-06-05: 429", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 23}, "response": "{\n  \"error\": {\n    \"code\": \"OVER_RATE_LIMIT\",\n    \"message\": \"You have exceeded your rate limit. Try again later or contact us at https://api.nasa.gov:443/contact/ for assistance\"\n  }\n}"}
{"timestamp": "2025-07-05 22:02:14", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=51.5074, lng=-0.1278, date=2025-05-06", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 24}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 51.5074, "lon": -0.1278, "date": "2025-05-06", "dim": 0.15}}
{"timestamp": "2025-07-05 22:02:15", "type": "API_ERROR", "message": "NASA API error for date 2025-05-06: 429", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 25}, "response": "{\n  \"error\": {\n    \"code\": \"OVER_RATE_LIMIT\",\n    \"message\": \"You have exceeded your rate limit. Try again later or contact us at https://api.nasa.gov:443/contact/ for assistance\"\n  }\n}"}
{"timestamp": "2025-07-05 22:02:16", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=51.5074, lng=-0.1278, date=2025-04-06", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 26}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 51.5074, "lon": -0.1278, "date": "2025-04-06", "dim": 0.15}}
{"timestamp": "2025-07-05 22:02:17", "type": "API_ERROR", "message": "NASA API error for date 2025-04-06: 429", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 27}, "response": "{\n  \"error\": {\n    \"code\": \"OVER_RATE_LIMIT\",\n    \"message\": \"You have exceeded your rate limit. Try again later or contact us at https://api.nasa.gov:443/contact/ for assistance\"\n  }\n}"}
{"timestamp": "2025-07-05 22:02:17", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=51.5074, lng=-0.1278, date=2023-06-01", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 28}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 51.5074, "lon": -0.1278, "date": "2023-06-01", "dim": 0.15}}
{"timestamp": "2025-07-05 22:02:18", "type": "API_ERROR", "message": "NASA API error for date 2023-06-01: 429", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 29}, "response": "{\n  \"error\": {\n    \"code\": \"OVER_RATE_LIMIT\",\n    \"message\": \"You have exceeded your rate limit. Try again later or contact us at https://api.nasa.gov:443/contact/ for assistance\"\n  }\n}"}
{"timestamp": "2025-07-05 22:02:19", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=51.5074, lng=-0.1278, date=2023-01-01", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 30}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 51.5074, "lon": -0.1278, "date": "2023-01-01", "dim": 0.15}}
{"timestamp": "2025-07-05 22:02:20", "type": "API_ERROR", "message": "NASA API error for date 2023-01-01: 429", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 31}, "response": "{\n  \"error\": {\n    \"code\": \"OVER_RATE_LIMIT\",\n    \"message\": \"You have exceeded your rate limit. Try again later or contact us at https://api.nasa.gov:443/contact/ for assistance\"\n  }\n}"}
{"timestamp": "2025-07-05 22:02:20", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 32}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 22:02:21", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=35.6762, lng=139.6503, date=2025-06-05", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 33}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 35.6762, "lon": 139.6503, "date": "2025-06-05", "dim": 0.15}}
{"timestamp": "2025-07-05 22:02:22", "type": "API_ERROR", "message": "NASA API error for date 2025-06-05: 429", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 34}, "response": "{\n  \"error\": {\n    \"code\": \"OVER_RATE_LIMIT\",\n    \"message\": \"You have exceeded your rate limit. Try again later or contact us at https://api.nasa.gov:443/contact/ for assistance\"\n  }\n}"}
{"timestamp": "2025-07-05 22:02:23", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=35.6762, lng=139.6503, date=2025-05-06", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 35}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 35.6762, "lon": 139.6503, "date": "2025-05-06", "dim": 0.15}}
{"timestamp": "2025-07-05 22:02:24", "type": "API_ERROR", "message": "NASA API error for date 2025-05-06: 429", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 36}, "response": "{\n  \"error\": {\n    \"code\": \"OVER_RATE_LIMIT\",\n    \"message\": \"You have exceeded your rate limit. Try again later or contact us at https://api.nasa.gov:443/contact/ for assistance\"\n  }\n}"}
{"timestamp": "2025-07-05 22:02:25", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=35.6762, lng=139.6503, date=2025-04-06", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 37}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 35.6762, "lon": 139.6503, "date": "2025-04-06", "dim": 0.15}}
{"timestamp": "2025-07-05 22:02:26", "type": "API_ERROR", "message": "NASA API error for date 2025-04-06: 429", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 38}, "response": "{\n  \"error\": {\n    \"code\": \"OVER_RATE_LIMIT\",\n    \"message\": \"You have exceeded your rate limit. Try again later or contact us at https://api.nasa.gov:443/contact/ for assistance\"\n  }\n}"}
{"timestamp": "2025-07-05 22:02:26", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=35.6762, lng=139.6503, date=2023-06-01", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 39}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 35.6762, "lon": 139.6503, "date": "2023-06-01", "dim": 0.15}}
{"timestamp": "2025-07-05 22:02:27", "type": "API_ERROR", "message": "NASA API error for date 2023-06-01: 429", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 40}, "response": "{\n  \"error\": {\n    \"code\": \"OVER_RATE_LIMIT\",\n    \"message\": \"You have exceeded your rate limit. Try again later or contact us at https://api.nasa.gov:443/contact/ for assistance\"\n  }\n}"}
{"timestamp": "2025-07-05 22:02:28", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=35.6762, lng=139.6503, date=2023-01-01", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 41}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 35.6762, "lon": 139.6503, "date": "2023-01-01", "dim": 0.15}}
{"timestamp": "2025-07-05 22:02:29", "type": "API_ERROR", "message": "NASA API error for date 2023-01-01: 429", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 42}, "response": "{\n  \"error\": {\n    \"code\": \"OVER_RATE_LIMIT\",\n    \"message\": \"You have exceeded your rate limit. Try again later or contact us at https://api.nasa.gov:443/contact/ for assistance\"\n  }\n}"}
{"timestamp": "2025-07-05 22:02:29", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 43}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 22:02:30", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=-33.8688, lng=151.2093, date=2025-06-05", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 44}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": -33.8688, "lon": 151.2093, "date": "2025-06-05", "dim": 0.15}}
{"timestamp": "2025-07-05 22:02:31", "type": "API_ERROR", "message": "NASA API error for date 2025-06-05: 429", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 45}, "response": "{\n  \"error\": {\n    \"code\": \"OVER_RATE_LIMIT\",\n    \"message\": \"You have exceeded your rate limit. Try again later or contact us at https://api.nasa.gov:443/contact/ for assistance\"\n  }\n}"}
{"timestamp": "2025-07-05 22:02:32", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=-33.8688, lng=151.2093, date=2025-05-06", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 46}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": -33.8688, "lon": 151.2093, "date": "2025-05-06", "dim": 0.15}}
{"timestamp": "2025-07-05 22:02:33", "type": "API_ERROR", "message": "NASA API error for date 2025-05-06: 429", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 47}, "response": "{\n  \"error\": {\n    \"code\": \"OVER_RATE_LIMIT\",\n    \"message\": \"You have exceeded your rate limit. Try again later or contact us at https://api.nasa.gov:443/contact/ for assistance\"\n  }\n}"}
{"timestamp": "2025-07-05 22:02:34", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=-33.8688, lng=151.2093, date=2025-04-06", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 48}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": -33.8688, "lon": 151.2093, "date": "2025-04-06", "dim": 0.15}}
{"timestamp": "2025-07-05 22:02:35", "type": "API_ERROR", "message": "NASA API error for date 2025-04-06: 429", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 49}, "response": "{\n  \"error\": {\n    \"code\": \"OVER_RATE_LIMIT\",\n    \"message\": \"You have exceeded your rate limit. Try again later or contact us at https://api.nasa.gov:443/contact/ for assistance\"\n  }\n}"}
{"timestamp": "2025-07-05 22:02:35", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=-33.8688, lng=151.2093, date=2023-06-01", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 50}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": -33.8688, "lon": 151.2093, "date": "2023-06-01", "dim": 0.15}}
{"timestamp": "2025-07-05 22:02:36", "type": "API_ERROR", "message": "NASA API error for date 2023-06-01: 429", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 51}, "response": "{\n  \"error\": {\n    \"code\": \"OVER_RATE_LIMIT\",\n    \"message\": \"You have exceeded your rate limit. Try again later or contact us at https://api.nasa.gov:443/contact/ for assistance\"\n  }\n}"}
{"timestamp": "2025-07-05 22:02:37", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=-33.8688, lng=151.2093, date=2023-01-01", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 52}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": -33.8688, "lon": 151.2093, "date": "2023-01-01", "dim": 0.15}}
{"timestamp": "2025-07-05 22:02:38", "type": "API_ERROR", "message": "NASA API error for date 2023-01-01: 429", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 53}, "response": "{\n  \"error\": {\n    \"code\": \"OVER_RATE_LIMIT\",\n    \"message\": \"You have exceeded your rate limit. Try again later or contact us at https://api.nasa.gov:443/contact/ for assistance\"\n  }\n}"}
{"timestamp": "2025-07-05 22:02:38", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 54}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 22:02:39", "type": "INPUT_VALIDATION", "message": "Invalid coordinate ranges: lat=999, lng=999", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 55}, "lat": 999, "lng": 999}
{"timestamp": "2025-07-05 22:02:41", "type": "INPUT_VALIDATION", "message": "Invalid coordinate ranges: lat=91, lng=0", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}, "lat": 91, "lng": 0}
{"timestamp": "2025-07-05 22:02:41", "type": "INPUT_VALIDATION", "message": "Invalid coordinate ranges: lat=-91, lng=0", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 1}, "lat": -91, "lng": 0}
{"timestamp": "2025-07-05 22:02:41", "type": "INPUT_VALIDATION", "message": "Invalid coordinate ranges: lat=0, lng=181", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 2}, "lat": 0, "lng": 181}
{"timestamp": "2025-07-05 22:02:41", "type": "INPUT_VALIDATION", "message": "Invalid coordinate ranges: lat=0, lng=-181", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 3}, "lat": 0, "lng": -181}
{"timestamp": "2025-07-05 22:02:41", "type": "INPUT_VALIDATION", "message": "Invalid coordinate types: lat=<class 'str'>, lng=<class 'str'>", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 4}, "lat": "40.7128", "lng": "-74.0060"}
{"timestamp": "2025-07-05 22:02:41", "type": "INPUT_VALIDATION", "message": "Invalid coordinate types: lat=<class 'NoneType'>, lng=<class 'NoneType'>", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 5}, "lat": null, "lng": null}
{"timestamp": "2025-07-05 22:02:41", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=89.9, lng=179.9, date=2025-06-05", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 6}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 89.9, "lon": 179.9, "date": "2025-06-05", "dim": 0.15}}
{"timestamp": "2025-07-05 22:02:42", "type": "API_ERROR", "message": "NASA API error for date 2025-06-05: 429", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 7}, "response": "{\n  \"error\": {\n    \"code\": \"OVER_RATE_LIMIT\",\n    \"message\": \"You have exceeded your rate limit. Try again later or contact us at https://api.nasa.gov:443/contact/ for assistance\"\n  }\n}"}
{"timestamp": "2025-07-05 22:02:42", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=89.9, lng=179.9, date=2025-05-06", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 8}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 89.9, "lon": 179.9, "date": "2025-05-06", "dim": 0.15}}
{"timestamp": "2025-07-05 22:02:43", "type": "API_ERROR", "message": "NASA API error for date 2025-05-06: 429", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 9}, "response": "{\n  \"error\": {\n    \"code\": \"OVER_RATE_LIMIT\",\n    \"message\": \"You have exceeded your rate limit. Try again later or contact us at https://api.nasa.gov:443/contact/ for assistance\"\n  }\n}"}
{"timestamp": "2025-07-05 22:02:44", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=89.9, lng=179.9, date=2025-04-06", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 10}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 89.9, "lon": 179.9, "date": "2025-04-06", "dim": 0.15}}
{"timestamp": "2025-07-05 22:02:45", "type": "API_ERROR", "message": "NASA API error for date 2025-04-06: 429", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 11}, "response": "{\n  \"error\": {\n    \"code\": \"OVER_RATE_LIMIT\",\n    \"message\": \"You have exceeded your rate limit. Try again later or contact us at https://api.nasa.gov:443/contact/ for assistance\"\n  }\n}"}
{"timestamp": "2025-07-05 22:02:45", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=89.9, lng=179.9, date=2023-06-01", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 12}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 89.9, "lon": 179.9, "date": "2023-06-01", "dim": 0.15}}
{"timestamp": "2025-07-05 22:02:47", "type": "API_ERROR", "message": "NASA API error for date 2023-06-01: 429", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 13}, "response": "{\n  \"error\": {\n    \"code\": \"OVER_RATE_LIMIT\",\n    \"message\": \"You have exceeded your rate limit. Try again later or contact us at https://api.nasa.gov:443/contact/ for assistance\"\n  }\n}"}
{"timestamp": "2025-07-05 22:02:47", "type": "API_REQUEST", "message": "Attempting NASA satellite image for lat=89.9, lng=179.9, date=2023-01-01", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 14}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 89.9, "lon": 179.9, "date": "2023-01-01", "dim": 0.15}}
{"timestamp": "2025-07-05 22:02:48", "type": "API_ERROR", "message": "NASA API error for date 2023-01-01: 429", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 15}, "response": "{\n  \"error\": {\n    \"code\": \"OVER_RATE_LIMIT\",\n    \"message\": \"You have exceeded your rate limit. Try again later or contact us at https://api.nasa.gov:443/contact/ for assistance\"\n  }\n}"}
{"timestamp": "2025-07-05 22:02:49", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 16}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 22:06:02", "type": "API_REQUEST", "message": "NASA API attempt 1: lat=40.7128, lng=-74.006, date=2025-06-05, dim=0.1", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 40.7128, "lon": -74.006, "date": "2025-06-05", "dim": 0.1}}
{"timestamp": "2025-07-05 22:06:03", "type": "RATE_LIMIT", "message": "NASA API rate limited", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 1}}
{"timestamp": "2025-07-05 22:06:08", "type": "API_REQUEST", "message": "NASA API attempt 1: lat=40.7128, lng=-74.006, date=2025-05-06, dim=0.1", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 2}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 40.7128, "lon": -74.006, "date": "2025-05-06", "dim": 0.1}}
{"timestamp": "2025-07-05 22:06:09", "type": "RATE_LIMIT", "message": "NASA API rate limited", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 3}}
{"timestamp": "2025-07-05 22:06:14", "type": "API_REQUEST", "message": "NASA API attempt 1: lat=40.7128, lng=-74.006, date=2025-04-06, dim=0.1", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 4}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 40.7128, "lon": -74.006, "date": "2025-04-06", "dim": 0.1}}
{"timestamp": "2025-07-05 22:06:15", "type": "RATE_LIMIT", "message": "NASA API rate limited", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 5}}
{"timestamp": "2025-07-05 22:06:20", "type": "API_REQUEST", "message": "NASA API attempt 1: lat=40.7128, lng=-74.006, date=2025-01-06, dim=0.1", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 6}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 40.7128, "lon": -74.006, "date": "2025-01-06", "dim": 0.1}}
{"timestamp": "2025-07-05 22:06:21", "type": "RATE_LIMIT", "message": "NASA API rate limited", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 7}}
{"timestamp": "2025-07-05 22:06:26", "type": "RETRY", "message": "NASA API retry 2 after 2s delay", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 8}}
{"timestamp": "2025-07-05 22:06:28", "type": "API_REQUEST", "message": "NASA API attempt 2: lat=40.7128, lng=-74.006, date=2025-06-05, dim=0.1", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 9}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 40.7128, "lon": -74.006, "date": "2025-06-05", "dim": 0.1}}
{"timestamp": "2025-07-05 22:06:29", "type": "RATE_LIMIT", "message": "NASA API rate limited", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 10}}
{"timestamp": "2025-07-05 22:06:34", "type": "RETRY", "message": "NASA API retry 2 after 2s delay", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 11}}
{"timestamp": "2025-07-05 22:06:36", "type": "API_REQUEST", "message": "NASA API attempt 2: lat=40.7128, lng=-74.006, date=2025-05-06, dim=0.1", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 12}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 40.7128, "lon": -74.006, "date": "2025-05-06", "dim": 0.1}}
{"timestamp": "2025-07-05 22:06:38", "type": "RATE_LIMIT", "message": "NASA API rate limited", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 13}}
{"timestamp": "2025-07-05 22:06:43", "type": "RETRY", "message": "NASA API retry 2 after 2s delay", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 14}}
{"timestamp": "2025-07-05 22:06:45", "type": "API_REQUEST", "message": "NASA API attempt 2: lat=40.7128, lng=-74.006, date=2025-04-06, dim=0.1", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 15}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 40.7128, "lon": -74.006, "date": "2025-04-06", "dim": 0.1}}
{"timestamp": "2025-07-05 22:06:46", "type": "RATE_LIMIT", "message": "NASA API rate limited", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 16}}
{"timestamp": "2025-07-05 22:06:51", "type": "RETRY", "message": "NASA API retry 2 after 2s delay", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 17}}
{"timestamp": "2025-07-05 22:06:53", "type": "API_REQUEST", "message": "NASA API attempt 2: lat=40.7128, lng=-74.006, date=2025-01-06, dim=0.1", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 18}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 40.7128, "lon": -74.006, "date": "2025-01-06", "dim": 0.1}}
{"timestamp": "2025-07-05 22:06:54", "type": "RATE_LIMIT", "message": "NASA API rate limited", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 19}}
{"timestamp": "2025-07-05 22:06:59", "type": "RETRY", "message": "NASA API retry 3 after 4s delay", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 20}}
{"timestamp": "2025-07-05 22:07:03", "type": "API_REQUEST", "message": "NASA API attempt 3: lat=40.7128, lng=-74.006, date=2025-06-05, dim=0.1", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 21}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 40.7128, "lon": -74.006, "date": "2025-06-05", "dim": 0.1}}
{"timestamp": "2025-07-05 22:07:04", "type": "RATE_LIMIT", "message": "NASA API rate limited", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 22}}
{"timestamp": "2025-07-05 22:07:09", "type": "RETRY", "message": "NASA API retry 3 after 4s delay", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 23}}
{"timestamp": "2025-07-05 22:07:13", "type": "API_REQUEST", "message": "NASA API attempt 3: lat=40.7128, lng=-74.006, date=2025-05-06, dim=0.1", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 24}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 40.7128, "lon": -74.006, "date": "2025-05-06", "dim": 0.1}}
{"timestamp": "2025-07-05 22:07:14", "type": "RATE_LIMIT", "message": "NASA API rate limited", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 25}}
{"timestamp": "2025-07-05 22:07:19", "type": "RETRY", "message": "NASA API retry 3 after 4s delay", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 26}}
{"timestamp": "2025-07-05 22:07:23", "type": "API_REQUEST", "message": "NASA API attempt 3: lat=40.7128, lng=-74.006, date=2025-04-06, dim=0.1", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 27}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 40.7128, "lon": -74.006, "date": "2025-04-06", "dim": 0.1}}
{"timestamp": "2025-07-05 22:07:24", "type": "RATE_LIMIT", "message": "NASA API rate limited", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 28}}
{"timestamp": "2025-07-05 22:07:29", "type": "RETRY", "message": "NASA API retry 3 after 4s delay", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 29}}
{"timestamp": "2025-07-05 22:07:33", "type": "API_REQUEST", "message": "NASA API attempt 3: lat=40.7128, lng=-74.006, date=2025-01-06, dim=0.1", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 30}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 40.7128, "lon": -74.006, "date": "2025-01-06", "dim": 0.1}}
{"timestamp": "2025-07-05 22:07:34", "type": "RATE_LIMIT", "message": "NASA API rate limited", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 31}}
{"timestamp": "2025-07-05 22:07:39", "type": "API_FAILURE", "message": "NASA API failed after 12 attempts", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 32}, "last_error": "Rate limit exceeded", "max_retries": 3}
{"timestamp": "2025-07-05 22:07:39", "type": "FALLBACK_VALIDATION", "message": "Validated fallback image: Professional Highlighted", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 33}, "path": "temp/satellite_professional_highlighted.jpg", "size": 179815, "dimensions": "640x640", "quality_score": 1.5}
{"timestamp": "2025-07-05 22:07:39", "type": "FALLBACK_VALIDATION", "message": "Validated fallback image: Single House", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 34}, "path": "temp/satellite_single_house.jpg", "size": 88382, "dimensions": "640x640", "quality_score": 1.38}
{"timestamp": "2025-07-05 22:07:39", "type": "FALLBACK_VALIDATION", "message": "Validated fallback image: Test Satellite", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 35}, "path": "temp/test_satellite.jpg", "size": 11061, "dimensions": "800x800", "quality_score": 0.89}
{"timestamp": "2025-07-05 22:07:39", "type": "FALLBACK_SELECTION", "message": "Selected best fallback image: Professional Highlighted", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 36}, "selected": "Professional Highlighted", "total_available": 3, "quality_score": 1.5, "location_context": {"hemisphere": "Northern", "continent": "Americas", "coordinates": {"lat": 40.7128, "lng": -74.006}}}
{"timestamp": "2025-07-05 22:07:39", "type": "API_REQUEST", "message": "NASA API attempt 1: lat=34.0522, lng=-118.2437, date=2025-06-05, dim=0.1", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 37}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 34.0522, "lon": -118.2437, "date": "2025-06-05", "dim": 0.1}}
{"timestamp": "2025-07-05 22:07:40", "type": "RATE_LIMIT", "message": "NASA API rate limited", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 38}}
{"timestamp": "2025-07-05 22:07:45", "type": "API_REQUEST", "message": "NASA API attempt 1: lat=34.0522, lng=-118.2437, date=2025-05-06, dim=0.1", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 39}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 34.0522, "lon": -118.2437, "date": "2025-05-06", "dim": 0.1}}
{"timestamp": "2025-07-05 22:07:46", "type": "RATE_LIMIT", "message": "NASA API rate limited", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 40}}
{"timestamp": "2025-07-05 22:07:51", "type": "API_REQUEST", "message": "NASA API attempt 1: lat=34.0522, lng=-118.2437, date=2025-04-06, dim=0.1", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 41}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 34.0522, "lon": -118.2437, "date": "2025-04-06", "dim": 0.1}}
{"timestamp": "2025-07-05 22:07:53", "type": "RATE_LIMIT", "message": "NASA API rate limited", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 42}}
{"timestamp": "2025-07-05 22:07:58", "type": "API_REQUEST", "message": "NASA API attempt 1: lat=34.0522, lng=-118.2437, date=2025-01-06, dim=0.1", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 43}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 34.0522, "lon": -118.2437, "date": "2025-01-06", "dim": 0.1}}
{"timestamp": "2025-07-05 22:07:59", "type": "RATE_LIMIT", "message": "NASA API rate limited", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 44}}
{"timestamp": "2025-07-05 22:08:04", "type": "RETRY", "message": "NASA API retry 2 after 2s delay", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 45}}
{"timestamp": "2025-07-05 22:08:06", "type": "API_REQUEST", "message": "NASA API attempt 2: lat=34.0522, lng=-118.2437, date=2025-06-05, dim=0.1", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 46}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 34.0522, "lon": -118.2437, "date": "2025-06-05", "dim": 0.1}}
{"timestamp": "2025-07-05 22:08:07", "type": "RATE_LIMIT", "message": "NASA API rate limited", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 47}}
{"timestamp": "2025-07-05 22:08:12", "type": "RETRY", "message": "NASA API retry 2 after 2s delay", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 48}}
{"timestamp": "2025-07-05 22:08:14", "type": "API_REQUEST", "message": "NASA API attempt 2: lat=34.0522, lng=-118.2437, date=2025-05-06, dim=0.1", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 49}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 34.0522, "lon": -118.2437, "date": "2025-05-06", "dim": 0.1}}
{"timestamp": "2025-07-05 22:08:15", "type": "RATE_LIMIT", "message": "NASA API rate limited", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 50}}
{"timestamp": "2025-07-05 22:08:20", "type": "RETRY", "message": "NASA API retry 2 after 2s delay", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 51}}
{"timestamp": "2025-07-05 22:08:22", "type": "API_REQUEST", "message": "NASA API attempt 2: lat=34.0522, lng=-118.2437, date=2025-04-06, dim=0.1", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 52}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 34.0522, "lon": -118.2437, "date": "2025-04-06", "dim": 0.1}}
{"timestamp": "2025-07-05 22:08:23", "type": "RATE_LIMIT", "message": "NASA API rate limited", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 53}}
{"timestamp": "2025-07-05 22:08:28", "type": "RETRY", "message": "NASA API retry 2 after 2s delay", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 54}}
{"timestamp": "2025-07-05 22:08:30", "type": "API_REQUEST", "message": "NASA API attempt 2: lat=34.0522, lng=-118.2437, date=2025-01-06, dim=0.1", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 55}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 34.0522, "lon": -118.2437, "date": "2025-01-06", "dim": 0.1}}
{"timestamp": "2025-07-05 22:08:31", "type": "RATE_LIMIT", "message": "NASA API rate limited", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 56}}
{"timestamp": "2025-07-05 22:08:36", "type": "RETRY", "message": "NASA API retry 3 after 4s delay", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 57}}
{"timestamp": "2025-07-05 22:08:40", "type": "API_REQUEST", "message": "NASA API attempt 3: lat=34.0522, lng=-118.2437, date=2025-06-05, dim=0.1", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 58}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 34.0522, "lon": -118.2437, "date": "2025-06-05", "dim": 0.1}}
{"timestamp": "2025-07-05 22:08:41", "type": "RATE_LIMIT", "message": "NASA API rate limited", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 59}}
{"timestamp": "2025-07-05 22:08:46", "type": "RETRY", "message": "NASA API retry 3 after 4s delay", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 60}}
{"timestamp": "2025-07-05 22:08:50", "type": "API_REQUEST", "message": "NASA API attempt 3: lat=34.0522, lng=-118.2437, date=2025-05-06, dim=0.1", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 61}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 34.0522, "lon": -118.2437, "date": "2025-05-06", "dim": 0.1}}
{"timestamp": "2025-07-05 22:08:51", "type": "RATE_LIMIT", "message": "NASA API rate limited", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 62}}
{"timestamp": "2025-07-05 22:08:56", "type": "RETRY", "message": "NASA API retry 3 after 4s delay", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 63}}
{"timestamp": "2025-07-05 22:09:00", "type": "API_REQUEST", "message": "NASA API attempt 3: lat=34.0522, lng=-118.2437, date=2025-04-06, dim=0.1", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 64}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 34.0522, "lon": -118.2437, "date": "2025-04-06", "dim": 0.1}}
{"timestamp": "2025-07-05 22:09:02", "type": "RATE_LIMIT", "message": "NASA API rate limited", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 65}}
{"timestamp": "2025-07-05 22:09:07", "type": "RETRY", "message": "NASA API retry 3 after 4s delay", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 66}}
{"timestamp": "2025-07-05 22:09:11", "type": "API_REQUEST", "message": "NASA API attempt 3: lat=34.0522, lng=-118.2437, date=2025-01-06, dim=0.1", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 67}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 34.0522, "lon": -118.2437, "date": "2025-01-06", "dim": 0.1}}
{"timestamp": "2025-07-05 22:09:12", "type": "RATE_LIMIT", "message": "NASA API rate limited", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 68}}
{"timestamp": "2025-07-05 22:09:17", "type": "API_FAILURE", "message": "NASA API failed after 12 attempts", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 69}, "last_error": "Rate limit exceeded", "max_retries": 3}
{"timestamp": "2025-07-05 22:09:17", "type": "FALLBACK_VALIDATION", "message": "Validated fallback image: Professional Highlighted", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 70}, "path": "temp/satellite_professional_highlighted.jpg", "size": 179815, "dimensions": "640x640", "quality_score": 1.5}
{"timestamp": "2025-07-05 22:09:17", "type": "FALLBACK_VALIDATION", "message": "Validated fallback image: Single House", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 71}, "path": "temp/satellite_single_house.jpg", "size": 88382, "dimensions": "640x640", "quality_score": 1.38}
{"timestamp": "2025-07-05 22:09:17", "type": "FALLBACK_VALIDATION", "message": "Validated fallback image: Test Satellite", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 72}, "path": "temp/test_satellite.jpg", "size": 11061, "dimensions": "800x800", "quality_score": 0.89}
{"timestamp": "2025-07-05 22:09:17", "type": "FALLBACK_SELECTION", "message": "Selected best fallback image: Professional Highlighted", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 73}, "selected": "Professional Highlighted", "total_available": 3, "quality_score": 1.5, "location_context": {"hemisphere": "Northern", "continent": "Americas", "coordinates": {"lat": 34.0522, "lng": -118.2437}}}
{"timestamp": "2025-07-05 22:09:17", "type": "API_REQUEST", "message": "NASA API attempt 1: lat=40.7128, lng=-74.006, date=2025-06-05, dim=0.1", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 40.7128, "lon": -74.006, "date": "2025-06-05", "dim": 0.1}}
{"timestamp": "2025-07-05 22:09:18", "type": "RATE_LIMIT", "message": "NASA API rate limited", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 1}}
{"timestamp": "2025-07-05 22:09:23", "type": "API_REQUEST", "message": "NASA API attempt 1: lat=40.7128, lng=-74.006, date=2025-05-06, dim=0.1", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 2}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 40.7128, "lon": -74.006, "date": "2025-05-06", "dim": 0.1}}
{"timestamp": "2025-07-05 22:09:24", "type": "RATE_LIMIT", "message": "NASA API rate limited", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 3}}
{"timestamp": "2025-07-05 22:09:29", "type": "API_REQUEST", "message": "NASA API attempt 1: lat=40.7128, lng=-74.006, date=2025-04-06, dim=0.1", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 4}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 40.7128, "lon": -74.006, "date": "2025-04-06", "dim": 0.1}}
{"timestamp": "2025-07-05 22:09:30", "type": "RATE_LIMIT", "message": "NASA API rate limited", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 5}}
{"timestamp": "2025-07-05 22:09:35", "type": "API_REQUEST", "message": "NASA API attempt 1: lat=40.7128, lng=-74.006, date=2025-01-06, dim=0.1", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 6}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 40.7128, "lon": -74.006, "date": "2025-01-06", "dim": 0.1}}
{"timestamp": "2025-07-05 22:09:36", "type": "RATE_LIMIT", "message": "NASA API rate limited", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 7}}
{"timestamp": "2025-07-05 22:09:41", "type": "RETRY", "message": "NASA API retry 2 after 2s delay", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 8}}
{"timestamp": "2025-07-05 22:09:43", "type": "API_REQUEST", "message": "NASA API attempt 2: lat=40.7128, lng=-74.006, date=2025-06-05, dim=0.1", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 9}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 40.7128, "lon": -74.006, "date": "2025-06-05", "dim": 0.1}}
{"timestamp": "2025-07-05 22:09:44", "type": "RATE_LIMIT", "message": "NASA API rate limited", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 10}}
{"timestamp": "2025-07-05 22:09:49", "type": "RETRY", "message": "NASA API retry 2 after 2s delay", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 11}}
{"timestamp": "2025-07-05 22:09:51", "type": "API_REQUEST", "message": "NASA API attempt 2: lat=40.7128, lng=-74.006, date=2025-05-06, dim=0.1", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 12}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 40.7128, "lon": -74.006, "date": "2025-05-06", "dim": 0.1}}
{"timestamp": "2025-07-05 22:09:53", "type": "RATE_LIMIT", "message": "NASA API rate limited", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 13}}
{"timestamp": "2025-07-05 22:09:58", "type": "RETRY", "message": "NASA API retry 2 after 2s delay", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 14}}
{"timestamp": "2025-07-05 22:10:00", "type": "API_REQUEST", "message": "NASA API attempt 2: lat=40.7128, lng=-74.006, date=2025-04-06, dim=0.1", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 15}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 40.7128, "lon": -74.006, "date": "2025-04-06", "dim": 0.1}}
{"timestamp": "2025-07-05 22:10:01", "type": "RATE_LIMIT", "message": "NASA API rate limited", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 16}}
{"timestamp": "2025-07-05 22:10:06", "type": "RETRY", "message": "NASA API retry 2 after 2s delay", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 17}}
{"timestamp": "2025-07-05 22:10:08", "type": "API_REQUEST", "message": "NASA API attempt 2: lat=40.7128, lng=-74.006, date=2025-01-06, dim=0.1", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 18}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 40.7128, "lon": -74.006, "date": "2025-01-06", "dim": 0.1}}
{"timestamp": "2025-07-05 22:10:09", "type": "RATE_LIMIT", "message": "NASA API rate limited", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 19}}
{"timestamp": "2025-07-05 22:10:14", "type": "RETRY", "message": "NASA API retry 3 after 4s delay", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 20}}
{"timestamp": "2025-07-05 22:10:18", "type": "API_REQUEST", "message": "NASA API attempt 3: lat=40.7128, lng=-74.006, date=2025-06-05, dim=0.1", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 21}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 40.7128, "lon": -74.006, "date": "2025-06-05", "dim": 0.1}}
{"timestamp": "2025-07-05 22:10:19", "type": "RATE_LIMIT", "message": "NASA API rate limited", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 22}}
{"timestamp": "2025-07-05 22:10:24", "type": "RETRY", "message": "NASA API retry 3 after 4s delay", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 23}}
{"timestamp": "2025-07-05 22:10:28", "type": "API_REQUEST", "message": "NASA API attempt 3: lat=40.7128, lng=-74.006, date=2025-05-06, dim=0.1", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 24}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 40.7128, "lon": -74.006, "date": "2025-05-06", "dim": 0.1}}
{"timestamp": "2025-07-05 22:10:29", "type": "RATE_LIMIT", "message": "NASA API rate limited", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 25}}
{"timestamp": "2025-07-05 22:10:34", "type": "RETRY", "message": "NASA API retry 3 after 4s delay", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 26}}
{"timestamp": "2025-07-05 22:10:38", "type": "API_REQUEST", "message": "NASA API attempt 3: lat=40.7128, lng=-74.006, date=2025-04-06, dim=0.1", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 27}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 40.7128, "lon": -74.006, "date": "2025-04-06", "dim": 0.1}}
{"timestamp": "2025-07-05 22:10:39", "type": "RATE_LIMIT", "message": "NASA API rate limited", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 28}}
{"timestamp": "2025-07-05 22:10:44", "type": "RETRY", "message": "NASA API retry 3 after 4s delay", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 29}}
{"timestamp": "2025-07-05 22:10:48", "type": "API_REQUEST", "message": "NASA API attempt 3: lat=40.7128, lng=-74.006, date=2025-01-06, dim=0.1", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 30}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 40.7128, "lon": -74.006, "date": "2025-01-06", "dim": 0.1}}
{"timestamp": "2025-07-05 22:10:49", "type": "RATE_LIMIT", "message": "NASA API rate limited", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 31}}
{"timestamp": "2025-07-05 22:10:54", "type": "API_FAILURE", "message": "NASA API failed after 12 attempts", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 32}, "last_error": "Rate limit exceeded", "max_retries": 3}
{"timestamp": "2025-07-05 22:10:54", "type": "FALLBACK_VALIDATION", "message": "Validated fallback image: Professional Highlighted", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 33}, "path": "temp/satellite_professional_highlighted.jpg", "size": 179815, "dimensions": "640x640", "quality_score": 1.5}
{"timestamp": "2025-07-05 22:10:54", "type": "FALLBACK_VALIDATION", "message": "Validated fallback image: Single House", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 34}, "path": "temp/satellite_single_house.jpg", "size": 88382, "dimensions": "640x640", "quality_score": 1.38}
{"timestamp": "2025-07-05 22:10:54", "type": "FALLBACK_VALIDATION", "message": "Validated fallback image: Test Satellite", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 35}, "path": "temp/test_satellite.jpg", "size": 11061, "dimensions": "800x800", "quality_score": 0.89}
{"timestamp": "2025-07-05 22:10:54", "type": "FALLBACK_SELECTION", "message": "Selected best fallback image: Professional Highlighted", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 36}, "selected": "Professional Highlighted", "total_available": 3, "quality_score": 1.5, "location_context": {"hemisphere": "Northern", "continent": "Americas", "coordinates": {"lat": 40.7128, "lng": -74.006}}}
{"timestamp": "2025-07-05 22:10:54", "type": "API_REQUEST", "message": "NASA API attempt 1: lat=51.5074, lng=-0.1278, date=2025-06-05, dim=0.1", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 37}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 51.5074, "lon": -0.1278, "date": "2025-06-05", "dim": 0.1}}
{"timestamp": "2025-07-05 22:10:55", "type": "RATE_LIMIT", "message": "NASA API rate limited", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 38}}
{"timestamp": "2025-07-05 22:11:00", "type": "API_REQUEST", "message": "NASA API attempt 1: lat=51.5074, lng=-0.1278, date=2025-05-06, dim=0.1", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 39}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 51.5074, "lon": -0.1278, "date": "2025-05-06", "dim": 0.1}}
{"timestamp": "2025-07-05 22:11:02", "type": "RATE_LIMIT", "message": "NASA API rate limited", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 40}}
{"timestamp": "2025-07-05 22:11:07", "type": "API_REQUEST", "message": "NASA API attempt 1: lat=51.5074, lng=-0.1278, date=2025-04-06, dim=0.1", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 41}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 51.5074, "lon": -0.1278, "date": "2025-04-06", "dim": 0.1}}
{"timestamp": "2025-07-05 22:11:08", "type": "RATE_LIMIT", "message": "NASA API rate limited", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 42}}
{"timestamp": "2025-07-05 22:11:13", "type": "API_REQUEST", "message": "NASA API attempt 1: lat=51.5074, lng=-0.1278, date=2025-01-06, dim=0.1", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 43}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 51.5074, "lon": -0.1278, "date": "2025-01-06", "dim": 0.1}}
{"timestamp": "2025-07-05 22:11:14", "type": "RATE_LIMIT", "message": "NASA API rate limited", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 44}}
{"timestamp": "2025-07-05 22:11:19", "type": "RETRY", "message": "NASA API retry 2 after 2s delay", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 45}}
{"timestamp": "2025-07-05 22:11:21", "type": "API_REQUEST", "message": "NASA API attempt 2: lat=51.5074, lng=-0.1278, date=2025-06-05, dim=0.1", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 46}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 51.5074, "lon": -0.1278, "date": "2025-06-05", "dim": 0.1}}
{"timestamp": "2025-07-05 22:11:22", "type": "RATE_LIMIT", "message": "NASA API rate limited", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 47}}
{"timestamp": "2025-07-05 22:11:27", "type": "RETRY", "message": "NASA API retry 2 after 2s delay", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 48}}
{"timestamp": "2025-07-05 22:11:29", "type": "API_REQUEST", "message": "NASA API attempt 2: lat=51.5074, lng=-0.1278, date=2025-05-06, dim=0.1", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 49}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 51.5074, "lon": -0.1278, "date": "2025-05-06", "dim": 0.1}}
{"timestamp": "2025-07-05 22:11:30", "type": "RATE_LIMIT", "message": "NASA API rate limited", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 50}}
{"timestamp": "2025-07-05 22:11:35", "type": "RETRY", "message": "NASA API retry 2 after 2s delay", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 51}}
{"timestamp": "2025-07-05 22:11:37", "type": "API_REQUEST", "message": "NASA API attempt 2: lat=51.5074, lng=-0.1278, date=2025-04-06, dim=0.1", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 52}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 51.5074, "lon": -0.1278, "date": "2025-04-06", "dim": 0.1}}
{"timestamp": "2025-07-05 22:11:38", "type": "RATE_LIMIT", "message": "NASA API rate limited", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 53}}
{"timestamp": "2025-07-05 22:11:43", "type": "RETRY", "message": "NASA API retry 2 after 2s delay", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 54}}
{"timestamp": "2025-07-05 22:11:45", "type": "API_REQUEST", "message": "NASA API attempt 2: lat=51.5074, lng=-0.1278, date=2025-01-06, dim=0.1", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 55}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 51.5074, "lon": -0.1278, "date": "2025-01-06", "dim": 0.1}}
{"timestamp": "2025-07-05 22:11:46", "type": "RATE_LIMIT", "message": "NASA API rate limited", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 56}}
{"timestamp": "2025-07-05 22:11:51", "type": "RETRY", "message": "NASA API retry 3 after 4s delay", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 57}}
{"timestamp": "2025-07-05 22:11:55", "type": "API_REQUEST", "message": "NASA API attempt 3: lat=51.5074, lng=-0.1278, date=2025-06-05, dim=0.1", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 58}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 51.5074, "lon": -0.1278, "date": "2025-06-05", "dim": 0.1}}
{"timestamp": "2025-07-05 22:11:56", "type": "RATE_LIMIT", "message": "NASA API rate limited", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 59}}
{"timestamp": "2025-07-05 22:12:01", "type": "RETRY", "message": "NASA API retry 3 after 4s delay", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 60}}
{"timestamp": "2025-07-05 22:12:05", "type": "API_REQUEST", "message": "NASA API attempt 3: lat=51.5074, lng=-0.1278, date=2025-05-06, dim=0.1", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 61}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 51.5074, "lon": -0.1278, "date": "2025-05-06", "dim": 0.1}}
{"timestamp": "2025-07-05 22:12:07", "type": "RATE_LIMIT", "message": "NASA API rate limited", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 62}}
{"timestamp": "2025-07-05 22:12:12", "type": "RETRY", "message": "NASA API retry 3 after 4s delay", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 63}}
{"timestamp": "2025-07-05 22:12:16", "type": "API_REQUEST", "message": "NASA API attempt 3: lat=51.5074, lng=-0.1278, date=2025-04-06, dim=0.1", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 64}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 51.5074, "lon": -0.1278, "date": "2025-04-06", "dim": 0.1}}
{"timestamp": "2025-07-05 22:12:17", "type": "RATE_LIMIT", "message": "NASA API rate limited", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 65}}
{"timestamp": "2025-07-05 22:12:22", "type": "RETRY", "message": "NASA API retry 3 after 4s delay", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 66}}
{"timestamp": "2025-07-05 22:12:26", "type": "API_REQUEST", "message": "NASA API attempt 3: lat=51.5074, lng=-0.1278, date=2025-01-06, dim=0.1", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 67}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 51.5074, "lon": -0.1278, "date": "2025-01-06", "dim": 0.1}}
{"timestamp": "2025-07-05 22:12:27", "type": "RATE_LIMIT", "message": "NASA API rate limited", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 68}}
{"timestamp": "2025-07-05 22:12:32", "type": "API_FAILURE", "message": "NASA API failed after 12 attempts", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 69}, "last_error": "Rate limit exceeded", "max_retries": 3}
{"timestamp": "2025-07-05 22:12:32", "type": "FALLBACK_VALIDATION", "message": "Validated fallback image: Professional Highlighted", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 70}, "path": "temp/satellite_professional_highlighted.jpg", "size": 179815, "dimensions": "640x640", "quality_score": 1.5}
{"timestamp": "2025-07-05 22:12:32", "type": "FALLBACK_VALIDATION", "message": "Validated fallback image: Single House", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 71}, "path": "temp/satellite_single_house.jpg", "size": 88382, "dimensions": "640x640", "quality_score": 1.38}
{"timestamp": "2025-07-05 22:12:32", "type": "FALLBACK_VALIDATION", "message": "Validated fallback image: Test Satellite", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 72}, "path": "temp/test_satellite.jpg", "size": 11061, "dimensions": "800x800", "quality_score": 0.89}
{"timestamp": "2025-07-05 22:12:32", "type": "FALLBACK_SELECTION", "message": "Selected best fallback image: Professional Highlighted", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 73}, "selected": "Professional Highlighted", "total_available": 3, "quality_score": 1.5, "location_context": {"hemisphere": "Northern", "continent": "Europe/Africa", "coordinates": {"lat": 51.5074, "lng": -0.1278}}}
{"timestamp": "2025-07-05 22:12:32", "type": "API_REQUEST", "message": "NASA API attempt 1: lat=35.6762, lng=139.6503, date=2025-06-05, dim=0.1", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 74}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 35.6762, "lon": 139.6503, "date": "2025-06-05", "dim": 0.1}}
{"timestamp": "2025-07-05 22:12:33", "type": "RATE_LIMIT", "message": "NASA API rate limited", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 75}}
{"timestamp": "2025-07-05 22:12:38", "type": "API_REQUEST", "message": "NASA API attempt 1: lat=35.6762, lng=139.6503, date=2025-05-06, dim=0.1", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 76}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 35.6762, "lon": 139.6503, "date": "2025-05-06", "dim": 0.1}}
{"timestamp": "2025-07-05 22:12:39", "type": "RATE_LIMIT", "message": "NASA API rate limited", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 77}}
{"timestamp": "2025-07-05 22:12:44", "type": "API_REQUEST", "message": "NASA API attempt 1: lat=35.6762, lng=139.6503, date=2025-04-06, dim=0.1", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 78}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 35.6762, "lon": 139.6503, "date": "2025-04-06", "dim": 0.1}}
{"timestamp": "2025-07-05 22:12:45", "type": "RATE_LIMIT", "message": "NASA API rate limited", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 79}}
{"timestamp": "2025-07-05 22:12:50", "type": "API_REQUEST", "message": "NASA API attempt 1: lat=35.6762, lng=139.6503, date=2025-01-06, dim=0.1", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 80}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 35.6762, "lon": 139.6503, "date": "2025-01-06", "dim": 0.1}}
{"timestamp": "2025-07-05 22:12:51", "type": "RATE_LIMIT", "message": "NASA API rate limited", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 81}}
{"timestamp": "2025-07-05 22:12:56", "type": "RETRY", "message": "NASA API retry 2 after 2s delay", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 82}}
{"timestamp": "2025-07-05 22:12:58", "type": "API_REQUEST", "message": "NASA API attempt 2: lat=35.6762, lng=139.6503, date=2025-06-05, dim=0.1", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 83}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 35.6762, "lon": 139.6503, "date": "2025-06-05", "dim": 0.1}}
{"timestamp": "2025-07-05 22:12:59", "type": "RATE_LIMIT", "message": "NASA API rate limited", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 84}}
{"timestamp": "2025-07-05 22:13:04", "type": "RETRY", "message": "NASA API retry 2 after 2s delay", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 85}}
{"timestamp": "2025-07-05 22:13:06", "type": "API_REQUEST", "message": "NASA API attempt 2: lat=35.6762, lng=139.6503, date=2025-05-06, dim=0.1", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 86}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 35.6762, "lon": 139.6503, "date": "2025-05-06", "dim": 0.1}}
{"timestamp": "2025-07-05 22:13:08", "type": "RATE_LIMIT", "message": "NASA API rate limited", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 87}}
{"timestamp": "2025-07-05 22:13:13", "type": "RETRY", "message": "NASA API retry 2 after 2s delay", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 88}}
{"timestamp": "2025-07-05 22:13:15", "type": "API_REQUEST", "message": "NASA API attempt 2: lat=35.6762, lng=139.6503, date=2025-04-06, dim=0.1", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 89}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 35.6762, "lon": 139.6503, "date": "2025-04-06", "dim": 0.1}}
{"timestamp": "2025-07-05 22:13:16", "type": "RATE_LIMIT", "message": "NASA API rate limited", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 90}}
{"timestamp": "2025-07-05 22:13:21", "type": "RETRY", "message": "NASA API retry 2 after 2s delay", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 91}}
{"timestamp": "2025-07-05 22:13:23", "type": "API_REQUEST", "message": "NASA API attempt 2: lat=35.6762, lng=139.6503, date=2025-01-06, dim=0.1", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 92}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 35.6762, "lon": 139.6503, "date": "2025-01-06", "dim": 0.1}}
{"timestamp": "2025-07-05 22:13:24", "type": "RATE_LIMIT", "message": "NASA API rate limited", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 93}}
{"timestamp": "2025-07-05 22:13:29", "type": "RETRY", "message": "NASA API retry 3 after 4s delay", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 94}}
{"timestamp": "2025-07-05 22:13:33", "type": "API_REQUEST", "message": "NASA API attempt 3: lat=35.6762, lng=139.6503, date=2025-06-05, dim=0.1", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 95}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 35.6762, "lon": 139.6503, "date": "2025-06-05", "dim": 0.1}}
{"timestamp": "2025-07-05 22:13:34", "type": "RATE_LIMIT", "message": "NASA API rate limited", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 96}}
{"timestamp": "2025-07-05 22:13:39", "type": "RETRY", "message": "NASA API retry 3 after 4s delay", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 97}}
{"timestamp": "2025-07-05 22:13:43", "type": "API_REQUEST", "message": "NASA API attempt 3: lat=35.6762, lng=139.6503, date=2025-05-06, dim=0.1", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 98}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 35.6762, "lon": 139.6503, "date": "2025-05-06", "dim": 0.1}}
{"timestamp": "2025-07-05 22:13:44", "type": "RATE_LIMIT", "message": "NASA API rate limited", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 99}}
{"timestamp": "2025-07-05 22:13:49", "type": "RETRY", "message": "NASA API retry 3 after 4s delay", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 100}}
{"timestamp": "2025-07-05 22:13:53", "type": "API_REQUEST", "message": "NASA API attempt 3: lat=35.6762, lng=139.6503, date=2025-04-06, dim=0.1", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 101}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 35.6762, "lon": 139.6503, "date": "2025-04-06", "dim": 0.1}}
{"timestamp": "2025-07-05 22:13:54", "type": "RATE_LIMIT", "message": "NASA API rate limited", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 102}}
{"timestamp": "2025-07-05 22:13:59", "type": "RETRY", "message": "NASA API retry 3 after 4s delay", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 103}}
{"timestamp": "2025-07-05 22:14:03", "type": "API_REQUEST", "message": "NASA API attempt 3: lat=35.6762, lng=139.6503, date=2025-01-06, dim=0.1", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 104}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": 35.6762, "lon": 139.6503, "date": "2025-01-06", "dim": 0.1}}
{"timestamp": "2025-07-05 22:14:04", "type": "RATE_LIMIT", "message": "NASA API rate limited", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 105}}
{"timestamp": "2025-07-05 22:14:09", "type": "API_FAILURE", "message": "NASA API failed after 12 attempts", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 106}, "last_error": "Rate limit exceeded", "max_retries": 3}
{"timestamp": "2025-07-05 22:14:09", "type": "FALLBACK_VALIDATION", "message": "Validated fallback image: Professional Highlighted", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 107}, "path": "temp/satellite_professional_highlighted.jpg", "size": 179815, "dimensions": "640x640", "quality_score": 1.5}
{"timestamp": "2025-07-05 22:14:09", "type": "FALLBACK_VALIDATION", "message": "Validated fallback image: Single House", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 108}, "path": "temp/satellite_single_house.jpg", "size": 88382, "dimensions": "640x640", "quality_score": 1.38}
{"timestamp": "2025-07-05 22:14:09", "type": "FALLBACK_VALIDATION", "message": "Validated fallback image: Test Satellite", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 109}, "path": "temp/test_satellite.jpg", "size": 11061, "dimensions": "800x800", "quality_score": 0.89}
{"timestamp": "2025-07-05 22:14:09", "type": "FALLBACK_SELECTION", "message": "Selected best fallback image: Professional Highlighted", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 110}, "selected": "Professional Highlighted", "total_available": 3, "quality_score": 1.5, "location_context": {"hemisphere": "Northern", "continent": "Asia/Oceania", "coordinates": {"lat": 35.6762, "lng": 139.6503}}}
{"timestamp": "2025-07-05 22:14:09", "type": "API_REQUEST", "message": "NASA API attempt 1: lat=-33.8688, lng=151.2093, date=2025-06-05, dim=0.1", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 111}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": -33.8688, "lon": 151.2093, "date": "2025-06-05", "dim": 0.1}}
{"timestamp": "2025-07-05 22:14:10", "type": "RATE_LIMIT", "message": "NASA API rate limited", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 112}}
{"timestamp": "2025-07-05 22:14:15", "type": "API_REQUEST", "message": "NASA API attempt 1: lat=-33.8688, lng=151.2093, date=2025-05-06, dim=0.1", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 113}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": -33.8688, "lon": 151.2093, "date": "2025-05-06", "dim": 0.1}}
{"timestamp": "2025-07-05 22:14:16", "type": "RATE_LIMIT", "message": "NASA API rate limited", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 114}}
{"timestamp": "2025-07-05 22:14:21", "type": "API_REQUEST", "message": "NASA API attempt 1: lat=-33.8688, lng=151.2093, date=2025-04-06, dim=0.1", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 115}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": -33.8688, "lon": 151.2093, "date": "2025-04-06", "dim": 0.1}}
{"timestamp": "2025-07-05 22:14:23", "type": "RATE_LIMIT", "message": "NASA API rate limited", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 116}}
{"timestamp": "2025-07-05 22:14:28", "type": "API_REQUEST", "message": "NASA API attempt 1: lat=-33.8688, lng=151.2093, date=2025-01-06, dim=0.1", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 117}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": -33.8688, "lon": 151.2093, "date": "2025-01-06", "dim": 0.1}}
{"timestamp": "2025-07-05 22:14:29", "type": "RATE_LIMIT", "message": "NASA API rate limited", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 118}}
{"timestamp": "2025-07-05 22:14:34", "type": "RETRY", "message": "NASA API retry 2 after 2s delay", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 119}}
{"timestamp": "2025-07-05 22:14:36", "type": "API_REQUEST", "message": "NASA API attempt 2: lat=-33.8688, lng=151.2093, date=2025-06-05, dim=0.1", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 120}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": -33.8688, "lon": 151.2093, "date": "2025-06-05", "dim": 0.1}}
{"timestamp": "2025-07-05 22:14:37", "type": "RATE_LIMIT", "message": "NASA API rate limited", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 121}}
{"timestamp": "2025-07-05 22:14:42", "type": "RETRY", "message": "NASA API retry 2 after 2s delay", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 122}}
{"timestamp": "2025-07-05 22:14:44", "type": "API_REQUEST", "message": "NASA API attempt 2: lat=-33.8688, lng=151.2093, date=2025-05-06, dim=0.1", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 123}, "url": "https://api.nasa.gov/planetary/earth/imagery", "params": {"lat": -33.8688, "lon": 151.2093, "date": "2025-05-06", "dim": 0.1}}
