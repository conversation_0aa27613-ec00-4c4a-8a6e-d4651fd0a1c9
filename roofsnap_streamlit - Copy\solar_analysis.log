Solar Analysis System Log
========================================
{"timestamp": "2025-07-05 19:32:17", "type": "CONFIGURATION", "message": "Gemini API key not found", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}}
{"timestamp": "2025-07-05 19:32:18", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 1}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:32:18", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 2}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:32:18", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 3}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:32:18", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 4}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:32:18", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 5}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:32:18", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 6}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:32:18", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 7}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:32:18", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 8}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:32:18", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 9}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:32:18", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 10}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:32:18", "type": "RATE_LIMIT", "message": "API rate limit exceeded for nasa", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 11}, "limit": 10, "period": 60}
{"timestamp": "2025-07-05 19:32:43", "type": "CONFIGURATION", "message": "Gemini API key not found", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}}
{"timestamp": "2025-07-05 19:32:43", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 1}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:33:59", "type": "CONFIGURATION", "message": "Gemini API key not found", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}}
{"timestamp": "2025-07-05 19:34:02", "type": "CONFIGURATION", "message": "Gemini API key not found", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}}
{"timestamp": "2025-07-05 19:34:02", "type": "CONFIGURATION", "message": "Gemini API key not found", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}}
{"timestamp": "2025-07-05 19:34:02", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 1}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:02", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 2}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:02", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 3}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:02", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 4}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:02", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 5}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:02", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 6}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:02", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 7}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:02", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 8}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:02", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 9}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:02", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 10}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:02", "type": "RATE_LIMIT", "message": "API rate limit exceeded for nasa", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 11}, "limit": 10, "period": 60}
{"timestamp": "2025-07-05 19:34:02", "type": "CONFIGURATION", "message": "Gemini API key not found", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}}
{"timestamp": "2025-07-05 19:34:02", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 1}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:02", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 2}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:02", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 3}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:02", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 4}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:02", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 5}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:02", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 6}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:02", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 7}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:02", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 8}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:02", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 9}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:02", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 10}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:02", "type": "RATE_LIMIT", "message": "API rate limit exceeded for nasa", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 11}, "limit": 10, "period": 60}
{"timestamp": "2025-07-05 19:34:02", "type": "RATE_LIMIT", "message": "API rate limit exceeded for nasa", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 12}, "limit": 10, "period": 60}
{"timestamp": "2025-07-05 19:34:02", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 13}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:28", "type": "CONFIGURATION", "message": "Gemini API key not found", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}}
{"timestamp": "2025-07-05 19:34:28", "type": "CONFIGURATION", "message": "Gemini API key not found", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}}
{"timestamp": "2025-07-05 19:34:28", "type": "CONFIGURATION", "message": "Gemini API key not found", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}}
{"timestamp": "2025-07-05 19:34:28", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 1}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:28", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 2}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:28", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 3}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:28", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 4}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:28", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 5}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:28", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 6}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:28", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 7}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:28", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 8}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:28", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 9}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:28", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 10}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:28", "type": "RATE_LIMIT", "message": "API rate limit exceeded for nasa", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 11}, "limit": 10, "period": 60}
{"timestamp": "2025-07-05 19:34:28", "type": "CONFIGURATION", "message": "Gemini API key not found", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}}
{"timestamp": "2025-07-05 19:34:28", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 1}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:28", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 2}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:28", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 3}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:28", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 4}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:28", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 5}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:28", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 6}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:28", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 7}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:28", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 8}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:28", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 9}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:28", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 10}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:28", "type": "RATE_LIMIT", "message": "API rate limit exceeded for nasa", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 11}, "limit": 10, "period": 60}
{"timestamp": "2025-07-05 19:34:28", "type": "RATE_LIMIT", "message": "API rate limit exceeded for nasa", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 12}, "limit": 10, "period": 60}
{"timestamp": "2025-07-05 19:34:28", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 13}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:53", "type": "CONFIGURATION", "message": "Gemini API key not found", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}}
{"timestamp": "2025-07-05 19:34:53", "type": "CONFIGURATION", "message": "Gemini API key not found", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}}
{"timestamp": "2025-07-05 19:34:54", "type": "CONFIGURATION", "message": "Gemini API key not found", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}}
{"timestamp": "2025-07-05 19:34:54", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 1}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:54", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 2}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:54", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 3}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:54", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 4}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:54", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 5}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:54", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 6}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:54", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 7}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:54", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 8}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:54", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 9}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:54", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 10}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:54", "type": "RATE_LIMIT", "message": "API rate limit exceeded for nasa", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 11}, "limit": 10, "period": 60}
{"timestamp": "2025-07-05 19:34:54", "type": "CONFIGURATION", "message": "Gemini API key not found", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}}
{"timestamp": "2025-07-05 19:34:54", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 1}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:54", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 2}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:54", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 3}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:54", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 4}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:54", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 5}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:54", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 6}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:54", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 7}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:54", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 8}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:54", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 9}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:54", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 10}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:34:54", "type": "RATE_LIMIT", "message": "API rate limit exceeded for nasa", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 11}, "limit": 10, "period": 60}
{"timestamp": "2025-07-05 19:34:54", "type": "RATE_LIMIT", "message": "API rate limit exceeded for nasa", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 12}, "limit": 10, "period": 60}
{"timestamp": "2025-07-05 19:34:54", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 13}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:35:36", "type": "CONFIGURATION", "message": "Gemini API key not found", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}}
{"timestamp": "2025-07-05 19:35:36", "type": "CONFIGURATION", "message": "Gemini API key not found", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}}
{"timestamp": "2025-07-05 19:35:37", "type": "CONFIGURATION", "message": "Gemini API key not found", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}}
{"timestamp": "2025-07-05 19:35:37", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 1}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:35:37", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 2}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:35:37", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 3}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:35:37", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 4}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:35:37", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 5}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:35:37", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 6}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:35:37", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 7}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:35:37", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 8}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:35:37", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 9}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:35:37", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 10}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:35:37", "type": "RATE_LIMIT", "message": "API rate limit exceeded for nasa", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 11}, "limit": 10, "period": 60}
{"timestamp": "2025-07-05 19:35:37", "type": "CONFIGURATION", "message": "Gemini API key not found", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}}
{"timestamp": "2025-07-05 19:35:37", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 1}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:35:37", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 2}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:35:37", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 3}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:35:37", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 4}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:35:37", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 5}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:35:37", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 6}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:35:37", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 7}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:35:37", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 8}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:35:37", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 9}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:35:37", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 10}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:35:37", "type": "RATE_LIMIT", "message": "API rate limit exceeded for nasa", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 11}, "limit": 10, "period": 60}
{"timestamp": "2025-07-05 19:35:37", "type": "RATE_LIMIT", "message": "API rate limit exceeded for nasa", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 12}, "limit": 10, "period": 60}
{"timestamp": "2025-07-05 19:35:37", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 13}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:36:07", "type": "CONFIGURATION", "message": "Gemini API key not found", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}}
{"timestamp": "2025-07-05 19:36:07", "type": "CONFIGURATION", "message": "Gemini API key not found", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}}
{"timestamp": "2025-07-05 19:36:08", "type": "CONFIGURATION", "message": "Gemini API key not found", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}}
{"timestamp": "2025-07-05 19:36:08", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 1}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:36:08", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 2}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:36:08", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 3}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:36:08", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 4}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:36:08", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 5}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:36:08", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 6}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:36:08", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 7}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:36:08", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 8}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:36:08", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 9}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:36:08", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 10}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:36:08", "type": "RATE_LIMIT", "message": "API rate limit exceeded for nasa", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 11}, "limit": 10, "period": 60}
{"timestamp": "2025-07-05 19:36:08", "type": "CONFIGURATION", "message": "Gemini API key not found", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}}
{"timestamp": "2025-07-05 19:36:08", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 1}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:36:08", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 2}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:36:08", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 3}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:36:08", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 4}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:36:08", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 5}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:36:08", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 6}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:36:08", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 7}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:36:08", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 8}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:36:08", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 9}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:36:08", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 10}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:36:08", "type": "RATE_LIMIT", "message": "API rate limit exceeded for nasa", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 11}, "limit": 10, "period": 60}
{"timestamp": "2025-07-05 19:36:08", "type": "RATE_LIMIT", "message": "API rate limit exceeded for nasa", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 12}, "limit": 10, "period": 60}
{"timestamp": "2025-07-05 19:36:08", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 13}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:36:52", "type": "CONFIGURATION", "message": "Gemini API key not found", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}}
{"timestamp": "2025-07-05 19:36:53", "type": "CONFIGURATION", "message": "Gemini API key not found", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}}
{"timestamp": "2025-07-05 19:36:53", "type": "CONFIGURATION", "message": "Gemini API key not found", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}}
{"timestamp": "2025-07-05 19:36:53", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 1}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:36:53", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 2}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:36:53", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 3}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:36:53", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 4}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:36:53", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 5}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:36:53", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 6}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:36:53", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 7}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:36:53", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 8}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:36:53", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 9}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:36:53", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 10}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:36:53", "type": "RATE_LIMIT", "message": "API rate limit exceeded for nasa", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 11}, "limit": 10, "period": 60}
{"timestamp": "2025-07-05 19:36:54", "type": "CONFIGURATION", "message": "Gemini API key not found", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}}
{"timestamp": "2025-07-05 19:36:54", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 1}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:36:54", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 2}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:36:54", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 3}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:36:54", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 4}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:36:54", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 5}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:36:54", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 6}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:36:54", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 7}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:36:54", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 8}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:36:54", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 9}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:36:54", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 10}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:36:54", "type": "RATE_LIMIT", "message": "API rate limit exceeded for nasa", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 11}, "limit": 10, "period": 60}
{"timestamp": "2025-07-05 19:36:54", "type": "RATE_LIMIT", "message": "API rate limit exceeded for nasa", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 12}, "limit": 10, "period": 60}
{"timestamp": "2025-07-05 19:36:54", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 13}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:38:42", "type": "CONFIGURATION", "message": "Gemini API key not found", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}}
{"timestamp": "2025-07-05 19:38:43", "type": "CONFIGURATION", "message": "Gemini API key not found", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}}
{"timestamp": "2025-07-05 19:40:45", "type": "CONFIGURATION", "message": "Gemini API key not found", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}}
{"timestamp": "2025-07-05 19:40:45", "type": "CONFIGURATION", "message": "Gemini API key not found", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}}
{"timestamp": "2025-07-05 19:42:02", "type": "CONFIGURATION", "message": "Gemini API key not found", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}}
{"timestamp": "2025-07-05 19:42:02", "type": "CONFIGURATION", "message": "Gemini API key not found", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}}
{"timestamp": "2025-07-05 19:42:49", "type": "CONFIGURATION", "message": "Gemini API key not found", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}}
{"timestamp": "2025-07-05 19:42:50", "type": "CONFIGURATION", "message": "Gemini API key not found", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}}
{"timestamp": "2025-07-05 19:43:25", "type": "CONFIGURATION", "message": "Gemini API key not found", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}}
{"timestamp": "2025-07-05 19:43:25", "type": "CONFIGURATION", "message": "Gemini API key not found", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}}
{"timestamp": "2025-07-05 19:43:54", "type": "CONFIGURATION", "message": "Gemini API key not found", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}}
{"timestamp": "2025-07-05 19:43:54", "type": "CONFIGURATION", "message": "Gemini API key not found", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}}
{"timestamp": "2025-07-05 19:44:26", "type": "CONFIGURATION", "message": "Gemini API key not found", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}}
{"timestamp": "2025-07-05 19:44:26", "type": "CONFIGURATION", "message": "Gemini API key not found", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}}
{"timestamp": "2025-07-05 19:45:35", "type": "CONFIGURATION", "message": "Gemini API key not found", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}}
{"timestamp": "2025-07-05 19:45:35", "type": "CONFIGURATION", "message": "Gemini API key not found", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}}
{"timestamp": "2025-07-05 19:49:14", "type": "CONFIGURATION", "message": "Gemini API key not found", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}}
{"timestamp": "2025-07-05 19:49:26", "type": "CONFIGURATION", "message": "Gemini API key not found", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}}
{"timestamp": "2025-07-05 19:52:29", "type": "CONFIGURATION", "message": "Gemini API key not found", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}}
{"timestamp": "2025-07-05 19:54:19", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
{"timestamp": "2025-07-05 19:58:03", "type": "FALLBACK", "message": "Using test image fallback", "error_counts": {"api": 0, "calculation": 0, "io": 0, "other": 0}, "path": "temp/satellite_single_house.jpg", "size": 88382, "modified": "2025-07-05T18:39:03.822382", "checksum": "1aa09963e732b5dd06a22a441c791d56"}
